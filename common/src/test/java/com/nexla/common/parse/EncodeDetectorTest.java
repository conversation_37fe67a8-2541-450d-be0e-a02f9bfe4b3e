package com.nexla.common.parse;

import static com.nexla.common.FileUtils.joinStream;
import static com.nexla.common.parse.FilePropertiesDetector.MAX_LINES;
import static com.nexla.common.parse.ParserConfigs.DEFAULT_CHARSET_DETECT_THRESHOLD;
import static org.apache.commons.io.ByteOrderMark.UTF_16BE;
import static org.apache.commons.io.ByteOrderMark.UTF_16LE;
import static org.apache.commons.io.ByteOrderMark.UTF_32BE;
import static org.apache.commons.io.ByteOrderMark.UTF_32LE;
import static org.apache.commons.io.ByteOrderMark.UTF_8;
import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;

import com.nexla.test.UnitTests;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collection;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.input.BOMInputStream;
import org.junit.Test;
import org.junit.experimental.categories.Category;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

@RunWith(Parameterized.class)
@Category(UnitTests.class)
public class EncodeDetectorTest {

  private String file;
  private String encoding;

  @Parameters
  public static Collection<String[]> data() {
    return Arrays.asList(
        new String[][] {
          {"/ISO-8859-1.csv", "ISO8859_1"},
          {"/ISO-8859-1_small.csv", "ISO8859_1"},
          {"/ISO-8859-1_bigger.csv", "ISO8859_1"},
          {"/UTF_8.csv", "UTF8"},
          {"/UTF_16.csv", "UnicodeBigUnmarked"},
          {"/EUC_JP.csv", "EUC_JP"}
        });
  }

  public EncodeDetectorTest(String file, String encoding) {
    this.file = file;
    this.encoding = encoding;
  }

  @Test
  public void testDetect() throws Exception {
    InputStream fileStream = getStream(file);
    byte[] sample = FilePropertiesDetector.readSample(fileStream);
    FilePropertiesDetector detector = new FilePropertiesDetector(sample, MAX_LINES);
    Charset charset =
        detector.detectCharset(StandardCharsets.UTF_8.name(), DEFAULT_CHARSET_DETECT_THRESHOLD);

    InputStream restoredStream = joinStream(sample, fileStream);
    InputStreamReader streamReader = new InputStreamReader(restoredStream, charset);
    assertThat(encoding, is(streamReader.getEncoding()));
    InputStreamReader expectedReader = new InputStreamReader(getStream(file), encoding);
    assertThat(IOUtils.toString(streamReader), is(IOUtils.toString(expectedReader)));
    restoredStream.close();
  }

  private InputStream getStream(String file) {
    return new BOMInputStream(
        this.getClass().getResourceAsStream(file), UTF_8, UTF_16BE, UTF_16LE, UTF_32BE, UTF_32LE);
  }
}
