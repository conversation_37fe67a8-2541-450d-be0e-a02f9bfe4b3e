package com.nexla.common.compute.producer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.scala.DefaultScalaModule;
import com.nexla.common.compute.ComputeMetricEntry;
import com.nexla.common.compute.ComputeMetricName;
import com.nexla.common.compute.ComputeMetricSource;
import java.time.LocalDate;
import java.util.Set;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ComputeMetricsKafkaDtoTest {

  @Test
  @SneakyThrows
  public void serializationTest() {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());

    ComputeMetricsKafkaDto dto = createTestDto();
    String jsonString = objectMapper.writeValueAsString(dto);
    ComputeMetricsKafkaDto processed =
        objectMapper.readValue(jsonString, ComputeMetricsKafkaDto.class);
    Assertions.assertEquals(dto, processed);
  }

  @Test
  @SneakyThrows
  public void serializationTestWithScalaModule() {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    objectMapper.registerModule(new Jdk8Module());
    objectMapper.registerModule(new DefaultScalaModule());

    ComputeMetricsKafkaDto dto = createTestDto();
    String jsonString = objectMapper.writeValueAsString(dto);
    ComputeMetricsKafkaDto processed =
        objectMapper.readValue(jsonString, ComputeMetricsKafkaDto.class);
    Assertions.assertEquals(dto, processed);
  }

  private static ComputeMetricsKafkaDto createTestDto() {
    ComputeMetricsKafkaDto dto =
        ComputeMetricsKafkaDto.builder()
            .resourceId(100)
            .runId(10002L)
            .resourceType(ComputeMetricSource.DATASET)
            .reportingDate(LocalDate.now())
            .flowNodeId(200)
            .orgId(500)
            .ownerId(400)
            .metrics(Set.of(new ComputeMetricEntry(ComputeMetricName.PROCESSING_TIME, 1d)))
            .createdAt(123L)
            .build();
    return dto;
  }
}
