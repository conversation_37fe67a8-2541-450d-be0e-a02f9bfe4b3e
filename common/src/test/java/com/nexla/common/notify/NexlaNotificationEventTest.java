package com.nexla.common.notify;

import com.nexla.common.ResourceType;
import com.nexla.test.UnitTests;
import org.junit.Assert;
import org.junit.experimental.categories.Category;
import org.junit.jupiter.api.Test;

@Category(UnitTests.class)
class NexlaNotificationEventTest {

  @Test
  void buildDtoWithoutOriginNodeIdTest() {
    NexlaNotificationEvent nexlaNotificationEvent = new NexlaNotificationEvent();
    nexlaNotificationEvent.setResourceId(100);
    nexlaNotificationEvent.setResourceType(ResourceType.SOURCE);
    nexlaNotificationEvent.setEventSource("random event");

    Assert.assertNotNull(nexlaNotificationEvent);
    Assert.assertNull(nexlaNotificationEvent.getOriginNodeId());
  }
}
