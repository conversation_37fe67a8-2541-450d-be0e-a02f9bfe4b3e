package com.nexla.common;

import static java.util.concurrent.TimeUnit.MILLISECONDS;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class NexlaScheduledAccumulator<D> {
  private static final Logger LOGGER = LoggerFactory.getLogger(NexlaScheduledAccumulator.class);
  private static final ScheduledExecutorService SCHEDULER = Executors.newScheduledThreadPool(1);

  private final BiFunction<D, D, D> accumulator;
  private final Consumer<D> onScheduleCallback;
  private final ScheduledFuture<?> task;

  private D data;
  private boolean closed = false;

  protected NexlaScheduledAccumulator() {
    this.accumulator = null;
    this.onScheduleCallback = null;
    this.task = null;
  }

  public NexlaScheduledAccumulator(
      BiFunction<D, D, D> onAccumulate, Consumer<D> onScheduleCallback, Long periodMillis) {
    this(onAccumulate, onScheduleCallback, periodMillis, SCHEDULER);
  }

  public NexlaScheduledAccumulator(
      BiFunction<D, D, D> onAccumulate,
      Consumer<D> onScheduleCallback,
      Long periodMillis,
      ScheduledExecutorService executorService) {
    this(onAccumulate, onScheduleCallback, periodMillis, executorService, false);
  }

  public NexlaScheduledAccumulator(
      BiFunction<D, D, D> onAccumulate,
      Consumer<D> onScheduleCallback,
      Long periodMillis,
      ScheduledExecutorService executorService,
      boolean executeWithFixedDelay) {
    this.accumulator = onAccumulate;
    this.onScheduleCallback = onScheduleCallback;

    this.task =
        executeWithFixedDelay
            ? executorService.scheduleWithFixedDelay(
                this::onSchedule, periodMillis, periodMillis, MILLISECONDS)
            : executorService.scheduleAtFixedRate(
                this::onSchedule, periodMillis, periodMillis, MILLISECONDS);
  }

  public synchronized void onSchedule() {
    if (NexlaScheduledAccumulator.this.data != null) {
      onScheduleCallback.accept(data);
      this.data = null;
    }
  }

  public synchronized void accumulate(D newData) {
    if (closed) {
      throw new IllegalStateException("NexlaScheduledAccumulator is closed");
    }
    this.data = data == null ? newData : accumulator.apply(newData, this.data);
  }

  public synchronized void close() {
    if (!closed) {
      task.cancel(false);
      onSchedule();
      this.closed = true;
    }
  }
}
