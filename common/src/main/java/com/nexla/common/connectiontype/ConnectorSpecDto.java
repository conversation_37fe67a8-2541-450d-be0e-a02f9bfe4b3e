package com.nexla.common.connectiontype;

import com.nexla.common.ConnectionType;
import java.util.Optional;
import lombok.Data;

@Data
public class ConnectorSpecDto {

  public final String authConfigClass;
  public final Optional<ConfigSpecDto> sourceConfig;
  public final Optional<ConfigSpecDto> sinkConfig;
  public final ConnectionType connectionType;
  public final Optional<String> connectorServiceClass;
}
