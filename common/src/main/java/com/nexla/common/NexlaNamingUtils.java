package com.nexla.common;

import static com.nexla.common.ResourceType.SINK;
import static com.nexla.common.ResourceType.SOURCE;
import static java.lang.Integer.parseInt;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/** Naming utils for naming connectors & topics. Used by connectors and stream processors. */
public class NexlaNamingUtils {
  // "inmemory-<orgID>-<flowID>-<sourceID>"
  private static final Pattern DEDICATED_INMEMORY_SERVICE_NAME_PATTERN =
      Pattern.compile("^inmemory-(\\d+)-(\\d+)-(\\d+)$");

  // "replication-<orgID>-<flowID>-<sourceID>"
  private static final Pattern DEDICATED_REPLICATION_SERVICE_NAME_PATTERN =
      Pattern.compile("^replication-(\\d+)-(\\d+)-(\\d+)$");

  private static final Pattern SOURCE_SERVICE_NAME_PATTERN = Pattern.compile("^.+-source-(\\d+)$");
  private static final Pattern SINK_SERVICE_NAME_PATTERN = Pattern.compile("^.+-sink-(\\d+)$");

  public static boolean isDedicatedInMemoryServiceName(String name) {
    return DEDICATED_INMEMORY_SERVICE_NAME_PATTERN.matcher(name).matches();
  }

  public static boolean isDedicatedReplicationServiceName(String name) {
    return DEDICATED_REPLICATION_SERVICE_NAME_PATTERN.matcher(name).matches();
  }

  public static boolean isSourceServiceName(String name) {
    return SOURCE_SERVICE_NAME_PATTERN.matcher(name).matches();
  }

  public static boolean isSinkServiceName(String name) {
    return SINK_SERVICE_NAME_PATTERN.matcher(name).matches();
  }

  public static Resource resourceByConnectorName(String connectorName) {
    return new Resource(extractId(connectorName), extractResourceType(connectorName));
  }

  private static ResourceType extractResourceType(String connectorName) {
    return connectorName.startsWith("source-") ? SOURCE : SINK;
  }

  public static String connectorGroupId(Resource resource) {
    return "connect-" + connectorName(resource);
  }

  public static String transformDatasetsGroupId(Integer datasetFrom, Integer datasetTo) {
    return "transform-" + nameDataSetTopic(datasetFrom) + "-" + nameDataSetTopic(datasetTo);
  }

  public static String streamingConnectorGroupId(final Resource resource) {
    if (!(resource.isSink() || resource.isSource())) {
      throw new IllegalArgumentException(
          "Streaming group ID can be generated only for sources or sinks.");
    }
    return resource.type.toString().toLowerCase() + "_" + resource.id;
  }

  public static Integer extractId(String connectorName) {
    return parseInt(connectorName.split("-")[1]);
  }

  public static String inMemoryConnectorServiceName(int orgId, int flowId, int sourceId) {
    return String.format("inmemory-%d-%d-%d", orgId, flowId, sourceId);
  }

  public static String replicationConnectorServiceName(int orgId, int flowId, int sourceId) {
    return String.format("replication-%d-%d-%d", orgId, flowId, sourceId);
  }

  public static String sinkConnectorServiceName(Integer sinkId, ConnectionType connectionType) {
    return serviceName(sinkId, connectionType, SINK);
  }

  public static String sourceConnectorServiceName(Integer sourceId, ConnectionType connectionType) {
    return serviceName(sourceId, connectionType, SOURCE);
  }

  /** Get resource service name (like sink/source connectors) */
  public static String serviceName(
      Integer resourceId, ConnectionType connectionType, ResourceType resourceType) {
    return String.format(
        "%s-%s",
        connectionType.name().toLowerCase().replaceAll("_", "-"),
        connectorName(resourceId, resourceType));
  }

  public static String connectorName(Integer id, ResourceType resourceType) {
    return String.join("-", resourceType.name().toLowerCase(), id.toString());
  }

  public static Resource resourceFromServiceName(String serviceName) {
    Matcher sourceMatcher = SOURCE_SERVICE_NAME_PATTERN.matcher(serviceName);
    if (sourceMatcher.matches()) {
      return new Resource(Integer.parseInt(sourceMatcher.group(1)), SOURCE);
    }

    Matcher sinkMatcher = SINK_SERVICE_NAME_PATTERN.matcher(serviceName);
    if (sinkMatcher.matches()) {
      return new Resource(Integer.parseInt(sinkMatcher.group(1)), SINK);
    }

    Matcher inMemoryMatcher = DEDICATED_INMEMORY_SERVICE_NAME_PATTERN.matcher(serviceName);
    if (inMemoryMatcher.matches()) {
      return new Resource(Integer.parseInt(inMemoryMatcher.group(3)), SOURCE);
    }

    Matcher replicationMatcher = DEDICATED_REPLICATION_SERVICE_NAME_PATTERN.matcher(serviceName);
    if (replicationMatcher.matches()) {
      return new Resource(Integer.parseInt(replicationMatcher.group(3)), SOURCE);
    }

    throw new IllegalArgumentException(
        "Failed to parse resource from service name: " + serviceName);
  }

  public static String connectorName(Resource resource) {
    return String.join("-", resource.type.name().toLowerCase(), resource.id + "");
  }

  public static String nameDataSetTopic(Integer dataSetId) {
    return String.join("-", "dataset", dataSetId.toString());
  }

  public static String dbHistoryTopicName(Integer dataSourceId, String dbName) {
    return String.join("-", "dbhistory", dataSourceId.toString(), dbName);
  }

  public static boolean isDataSetTopic(String topic) {
    return topic.startsWith("dataset-");
  }

  public static boolean isQuarantineTopic(String topic) {
    return topic.startsWith("quarantine-");
  }

  public static String redisSourceQueueName(
      ConnectionType connectionType, Integer sourceId, Integer mapId, Optional<String> key) {
    String keyStr = key.map(s -> ":" + s).orElse("");
    return "queue:source:"
        + connectionType.name().toLowerCase()
        + ":"
        + sourceId
        + ":dm:entry:"
        + mapId
        + keyStr;
  }

  public static String getQuarantineTopic(Resource resource, Optional<Integer> id) {
    switch (resource.type) {
      case SOURCE:
      case SINK:
        return String.join(
            "-", "quarantine", resource.type.name().toLowerCase(), Integer.toString(resource.id));
      case DATASET:
        return String.join("-", "quarantine", nameDataSetTopic(id.orElse(resource.id)));
      default:
        throw new IllegalArgumentException("Unsupported resource type: " + resource.type);
    }
  }
}
