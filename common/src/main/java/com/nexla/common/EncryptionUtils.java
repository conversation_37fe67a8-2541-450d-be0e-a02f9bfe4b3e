package com.nexla.common;

import java.security.Key;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Base64;

public class EncryptionUtils {

  private static final String ALGORITHM = "AES";

  private final Key key;
  private final Cipher encryptCipher;
  private final Cipher decryptCipher;

  @SneakyThrows
  public EncryptionUtils(String secretKey) {
    this.key = generateKeyFromString(secretKey);
    this.encryptCipher = Cipher.getInstance(ALGORITHM);
    this.encryptCipher.init(Cipher.ENCRYPT_MODE, key);
    this.decryptCipher = Cipher.getInstance(ALGORITHM);
    this.decryptCipher.init(Cipher.DECRYPT_MODE, key);
  }

  @SneakyThrows
  public String encrypt(String valueEnc) {
    byte[] encValue = encryptCipher.doFinal(valueEnc.getBytes());
    return Base64.encodeBase64URLSafeString(encValue);
  }

  @SneakyThrows
  public String decrypt(String encryptedValue) {
    byte[] decorVal = Base64.decodeBase64(encryptedValue);
    byte[] decValue = decryptCipher.doFinal(decorVal);
    return new String(decValue);
  }

  @SneakyThrows
  private Key generateKeyFromString(String secKey) {
    byte[] keyVal = Base64.decodeBase64(secKey);
    return new SecretKeySpec(keyVal, ALGORITHM);
  }
}
