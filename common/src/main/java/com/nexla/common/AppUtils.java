package com.nexla.common;

import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Collections.singletonList;

import com.nexla.common.interceptor.RestTemplateLoggerInterceptor;
import com.nexla.common.request.HttpComponentsClientHttpRequestWithBodyFactory;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;
import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import lombok.SneakyThrows;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

public class AppUtils {

  private static final Logger LOGGER = LoggerFactory.getLogger(AppUtils.class);

  public static RestTemplate defaultRestTemplate(boolean bufferRequestBody, boolean logVerbose) {
    return constructTemplate(bufferRequestBody, HttpClients.createSystem(), 60_000, logVerbose);
  }

  public static RestTemplate nexlaRestTemplate(
      String pemCert, boolean bufferRequestBody, boolean logVerbose) {
    return constructTemplate(
        bufferRequestBody,
        createClosableHttpClient(sslContextWithPem(pemCert)),
        60_000,
        logVerbose);
  }

  public static RestTemplate nexlaRestTemplate(
      SSLCertificateStore k, SSLCertificateStore t, boolean bufferRequestBody, boolean logVerbose) {
    try {
      return AppUtils.constructTemplate(
          bufferRequestBody,
          AppUtils.createClosableHttpClient(AppUtils.sslContext(k, t)),
          60_000,
          logVerbose);
    } catch (NoSuchAlgorithmException
        | UnrecoverableKeyException
        | KeyStoreException
        | IOException
        | CertificateException
        | KeyManagementException e) {
      throw new RuntimeException(e);
    }
  }

  public static CloseableHttpClient createClosableHttpClient(SSLContext sslContext) {
    LOGGER.info("[HTTPS] Adding client certificate to TrustStore");
    SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(sslContext);
    return HttpClients.custom().setSSLSocketFactory(socketFactory).build();
  }

  public static RestTemplate constructTemplate(
      boolean bufferRequestBody, CloseableHttpClient client, int readTimeout, boolean logVerbose) {
    HttpComponentsClientHttpRequestFactory requestFactory =
        new HttpComponentsClientHttpRequestWithBodyFactory(client);
    requestFactory.setConnectTimeout(5_000);
    requestFactory.setReadTimeout(readTimeout);
    requestFactory.setBufferRequestBody(bufferRequestBody);

    // Create buffered request factory for buffered requests
    ClientHttpRequestFactory factory = requestFactory;
    if (bufferRequestBody) {
      // It is used for enabling LoggingInterceptor.
      // otherwise response body will be null after you read all bytes from InputStream.
      factory = new BufferingClientHttpRequestFactory(requestFactory);
    }

    RestTemplate restTemplate = new RestTemplate(factory);
    restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(UTF_8));

    // Interceptor is not working with non buffered requests
    if (bufferRequestBody) {
      List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
      if (CollectionUtils.isEmpty(interceptors)) {
        interceptors = new ArrayList<>();
      }
      interceptors.add(new RestTemplateLoggerInterceptor(logVerbose));
      restTemplate.setInterceptors(interceptors);
    }

    return restTemplate;
  }

  public static HttpHeaders authorizationHeader(String username, String password) {
    String authString = username + ":" + password;
    String authorizationHeader =
        "Basic " + new String(Base64.getEncoder().encode(authString.getBytes()));
    HttpHeaders headers = new HttpHeaders();
    headers.add("Authorization", authorizationHeader);
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setAccept(singletonList(MediaType.APPLICATION_JSON));
    return headers;
  }

  @SneakyThrows
  public static SSLContext sslContextWithPem(String pemFile) {
    KeyStore ts = PemKeyStore.createWithRandomPassword(pemFile).keyStore();
    TrustManagerFactory tmf =
        TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
    tmf.init(ts);

    SSLContext sslContext = SSLContext.getInstance("TLS");
    sslContext.init(
        null,
        new TrustManager[] {
          newCompositeTrustManagerOf(tmf.getTrustManagers(), defaultTrustManagers())
        },
        new SecureRandom());

    return sslContext;
  }

  private static TrustManager newCompositeTrustManagerOf(TrustManager[]... tms) {
    List<X509TrustManager> x509TrustManagers =
        Arrays.stream(tms)
            .flatMap(Arrays::stream)
            .filter(X509TrustManager.class::isInstance)
            .map(X509TrustManager.class::cast)
            .collect(Collectors.toList());

    return new CompositeX509TrustManager(x509TrustManagers);
  }

  public static SSLContext sslContext(SSLCertificateStore k, SSLCertificateStore t)
      throws NoSuchAlgorithmException,
          KeyStoreException,
          UnrecoverableKeyException,
          IOException,
          CertificateException,
          KeyManagementException {
    SSLContext sslContext = SSLContext.getInstance("TLS");

    sslContext.init(
        createKeyManagers(k.pkcs12(), k.getPassword()),
        new TrustManager[] {newCompositeTrustManagerOf(defaultTrustManagers(), trustManagers(t))},
        null);
    return sslContext;
  }

  private static TrustManager[] trustManagers(SSLCertificateStore t)
      throws NoSuchAlgorithmException, CertificateException, KeyStoreException, IOException {
    if (t == null) {
      return new TrustManager[0];
    }

    final TrustManagerFactory f =
        TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
    f.init(t.pkcs12());
    return f.getTrustManagers();
  }

  private static KeyManager[] createKeyManagers(KeyStore keystore, String pwd)
      throws UnrecoverableKeyException, KeyStoreException, NoSuchAlgorithmException {
    KeyManagerFactory f = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
    f.init(keystore, pwd.toCharArray());
    return f.getKeyManagers();
  }

  private static TrustManager[] defaultTrustManagers()
      throws NoSuchAlgorithmException, KeyStoreException {
    TrustManagerFactory defaultTmf =
        TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
    defaultTmf.init((KeyStore) null);
    return defaultTmf.getTrustManagers();
  }
}
