package com.nexla.common.request;

import java.net.URI;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpUriRequest;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;

public class HttpComponentsClientHttpRequestWithBodyFactory
    extends HttpComponentsClientHttpRequestFactory {

  @Override
  protected HttpUriRequest createHttpUriRequest(HttpMethod httpMethod, URI uri) {
    if (httpMethod == HttpMethod.GET) {
      return new HttpGetRequestWithEntity(uri);
    }
    return super.createHttpUriRequest(httpMethod, uri);
  }

  public HttpComponentsClientHttpRequestWithBodyFactory(HttpClient httpClient) {
    super(httpClient);
  }

  private static final class HttpGetRequestWithEntity extends HttpEntityEnclosingRequestBase {
    public HttpGetRequestWithEntity(final URI uri) {
      super.setURI(uri);
    }

    @Override
    public String getMethod() {
      return HttpMethod.GET.name();
    }
  }
}
