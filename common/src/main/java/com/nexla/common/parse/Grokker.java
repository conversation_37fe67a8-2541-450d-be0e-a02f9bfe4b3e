package com.nexla.common.parse;

import static java.util.Arrays.asList;

import com.google.common.collect.Maps;
import io.krakens.grok.api.Grok;
import io.krakens.grok.api.GrokCompiler;
import io.krakens.grok.api.Match;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import lombok.SneakyThrows;

public class Grokker {

  private static final List<String> GROK_PATTERNS_PATHS =
      asList(
          "/grok-patterns/firewalls",
          "/grok-patterns/grok-patterns",
          "/grok-patterns/haproxy",
          "/grok-patterns/java",
          "/grok-patterns/junos",
          "/grok-patterns/linux-syslog",
          "/grok-patterns/mongodb",
          "/grok-patterns/nagios",
          "/grok-patterns/postgresql",
          "/grok-patterns/redis",
          "/grok-patterns/ruby");

  public static Grokker INSTANCE = new Grokker();

  private final GrokCompiler grokCompiler;
  private final Map<String, Grok> patternToGrok;

  @SneakyThrows
  private Grokker() {
    this.patternToGrok = Maps.newHashMap();
    this.grokCompiler = GrokCompiler.newInstance();
    GROK_PATTERNS_PATHS.forEach(f -> register(grokCompiler, f));
  }

  public Map<String, Object> parseLog(String line, String pattern) {
    Grok grok = patternToGrok.computeIfAbsent(pattern, grokCompiler::compile);
    Match gm = grok.match(line);
    return gm.capture();
  }

  @SneakyThrows
  private void register(GrokCompiler grokCompiler, String resourcePath) {
    try (InputStream is = getClass().getResourceAsStream(resourcePath)) {
      grokCompiler.register(is);
    }
  }
}
