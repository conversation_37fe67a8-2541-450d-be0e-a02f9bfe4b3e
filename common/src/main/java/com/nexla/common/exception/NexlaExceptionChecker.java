package com.nexla.common.exception;

public class NexlaExceptionChecker {

  public static <T extends Throwable> boolean checkParentException(
      Throwable throwable, Class<T> clazz) {
    if (throwable == null || clazz == null) {
      return false;
    }

    // Check if the current exception is an Instance of the specified exception type
    if (clazz.isInstance(throwable)) {
      return true;
    }

    // Check the cause recursively
    return checkParentException(throwable.getCause(), clazz);
  }
}
