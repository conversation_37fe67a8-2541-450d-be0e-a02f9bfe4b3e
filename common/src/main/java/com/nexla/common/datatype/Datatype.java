package com.nexla.common.datatype;

import java.util.Set;

public enum Datatype {
  OBJECT("object"),
  INTEGER("integer"),
  STRING("string"),
  NULL("null"),
  BOOLEAN("boolean"),
  NUMBER("number"),
  UNKNOWN("unknown"),
  EMAIL("email"),
  IPV4("ipv4"),
  IPV6("ipv6"),
  USERAGENT("user-agent"),
  CURRENCY("currency"),
  DEVICEID("deviceId"),
  LOCATION("location"),
  VIN("vin"),
  TIME("time"),
  DATE("date"),
  TIMESTAMP("timestamp"),
  URL("url"),
  //	VISA("visa"),
  //	MASTERCARD("mastercard"),
  //	DISCOVER("discover"),
  //	AMEX("amex"),
  //	DINERS("diners"),
  //	JCB("jcb"),
  CREDIT_CARD("credit-card"),
  ADDRESS("address"),
  SSN("ssn"),
  PHONE_NUMBER("phone-number"),
  //	MD5("md5"),
  //	SHA256("sha256"),
  //	SHA_1("sha1"),
  HASH("hash");
  ;

  public static final String QUANTITY = "quantity";
  public final String format;
  public static final Set<Datatype> NUMBER_TYPES = Set.of(Datatype.NUMBER, Datatype.INTEGER);

  Datatype(String format) {
    this.format = format;
  }

  public static Datatype fromString(String format) {
    // backward compatibility to fix NEX-3229 caused by https://github.com/nexla/backend/pull/1065
    if (QUANTITY.equalsIgnoreCase(format)) {
      return INTEGER;
    }
    for (Datatype schemaType : Datatype.values()) {
      if (schemaType.format.equalsIgnoreCase(format)) {
        return schemaType;
      }
    }
    throw new IllegalArgumentException("No constant with type " + format + " found");
  }

  public String get() {
    return format;
  }
}
