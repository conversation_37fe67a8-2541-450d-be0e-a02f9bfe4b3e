package com.nexla.common.metrics;

import com.nexla.common.notify.transport.ControlMessageProducer;
import com.nexla.control.health.metrics.KafkaTimeEvent;
import com.nexla.control.message.MessageCreatedAt;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HealthMetricAggregator {

  private static final Logger LOGGER = LoggerFactory.getLogger(HealthMetricAggregator.class);
  private static HealthMetricAggregator INSTANCE = null;
  private static final ScheduledExecutorService SCHEDULED_EXECUTOR_SERVICE =
      Executors.newScheduledThreadPool(1);
  private static final long DELAY = 1;
  private static final TimeUnit TIME_UNIT = TimeUnit.MINUTES;
  private final Map<TopicGroupId, Long> map = new ConcurrentHashMap<>();
  private final ControlMessageProducer messageProducer;

  public static synchronized HealthMetricAggregator getInstance(
      ControlMessageProducer messageProducer) {
    if (INSTANCE == null) {
      INSTANCE = new HealthMetricAggregator(messageProducer);
    }

    return INSTANCE;
  }

  public static long getIntervalMs() {
    return TIME_UNIT.toMillis(DELAY);
  }

  private HealthMetricAggregator(ControlMessageProducer messageProducer) {
    this.messageProducer = messageProducer;
    SCHEDULED_EXECUTOR_SERVICE.scheduleWithFixedDelay(this::sendMetrics, 1, DELAY, TIME_UNIT);
  }

  public void collectTime(String topicName, String groupId, MessageCreatedAt messageCreatedAt) {
    long time =
        Optional.ofNullable(messageCreatedAt.getCreatedAt())
            .map(t -> Math.abs(System.currentTimeMillis() - t))
            .orElse(0L);

    TopicGroupId topicGroupId = new TopicGroupId(topicName, groupId);

    map.compute(topicGroupId, (gId, curTime) -> Math.max(time, (curTime == null) ? 0L : curTime));
  }

  private void sendMetrics() {
    map.forEach(
        (topicName, metricTime) -> {
          Long removedMetricTime = map.remove(topicName);
          messageProducer.sendHealthMetricEvent(
              new KafkaTimeEvent(topicName.topic, topicName.groupId, removedMetricTime));
        });
  }
}
