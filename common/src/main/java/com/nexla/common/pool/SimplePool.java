package com.nexla.common.pool;

import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.function.Consumer;
import java.util.function.Function;
import lombok.SneakyThrows;

/** Simple thread safe object pool */
public class SimplePool<P> implements NexlaPool<P> {

  private final BlockingQueue<P> pooledObjects;
  private final Optional<Consumer<P>> onClose;

  public SimplePool(int capacity, Consumer<P> onClose) {
    this.pooledObjects = new ArrayBlockingQueue<>(capacity);
    this.onClose = Optional.of(onClose);
  }

  public SimplePool(int capacity) {
    this.pooledObjects = new ArrayBlockingQueue<>(capacity);
    this.onClose = Optional.empty();
  }

  @SneakyThrows
  public void add(P object) {
    pooledObjects.put(object);
  }

  public <T> T withPooledObject(Function<P, T> fn) {
    P sender = take();
    try {
      return fn.apply(sender);
    } finally {
      add(sender);
    }
  }

  @Override
  public void withPooledObjectConsumer(Consumer<P> fn) {
    P sender = take();
    try {
      fn.accept(sender);
    } finally {
      add(sender);
    }
  }

  private P take() {
    return pooledObjects.poll();
  }

  public void close() {
    onClose.ifPresent(pooledObjects::forEach);
  }
}
