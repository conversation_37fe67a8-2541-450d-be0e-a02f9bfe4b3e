package com.nexla.common.notify;

import com.nexla.common.ResourceType;
import java.util.LinkedList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Predicate;
import org.apache.commons.lang3.tuple.Pair;

public class NotificationUtils {

  /**
   * Used to filter a stream by ResourceType and ResourceId
   *
   * @param resourceType if <code>null</code> include all else filter by resource type
   * @param resourceId if resourceType is null this param is ignored. if <code>null</code> include
   *     all else filter by resource ID
   * @return the instance of Predicate class to filter the stream
   */
  public static Predicate<Map.Entry<Pair<ResourceType, Integer>, ?>> filterBy(
      ResourceType resourceType, Integer resourceId) {
    return entry -> {
      if (resourceType == null) {
        return true;
      }

      Pair<ResourceType, Integer> mapKey = entry.getKey();
      if (mapKey.getLeft() != resourceType) {
        return false;
      }

      if (resourceId == null) {
        return true;
      }
      return mapKey.getRight().equals(resourceId);
    };
  }

  public static <T> void add(
      ConcurrentHashMap<Pair<ResourceType, Integer>, LinkedList<T>> map,
      ResourceType resourceType,
      Integer resourceId,
      T message,
      int maxErrors) {
    map.compute(
        Pair.of(resourceType, resourceId),
        (key, value) -> {
          if (value == null) {
            value = new LinkedList<>();
          }
          value.addLast(message);
          if (value.size() > maxErrors) {
            value.removeFirst();
          }
          return value;
        });
  }
}
