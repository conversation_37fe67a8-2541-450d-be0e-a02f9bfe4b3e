package com.nexla.common.notify;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.nexla.common.NexlaConstants;

public enum NotificationMedia {
  EMAIL(NexlaConstants.NOTIFY_MEDIA_EMAIL),
  SMS(NexlaConstants.NOTIFY_MEDIA_SMS),
  PUSHAPI(NexlaConstants.NOTIFY_MEDIA_PUSHAPI);

  private String key;

  NotificationMedia(String key) {
    this.key = key;
  }

  @JsonCreator
  public static NotificationMedia fromString(String key) {
    return NotificationMedia.valueOf(key.toUpperCase());
  }

  @JsonValue
  public String getKey() {
    return key;
  }
}
