package com.nexla.connector.config.ssh.tunnel;

import static java.util.Optional.ofNullable;

import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.nexla.common.logging.TimeLogger;
import com.nexla.connector.config.ssh.HostPort;
import com.nexla.connector.config.ssh.SshTunnelConfig;
import com.nexla.connector.config.ssh.SshTunnelStep;
import com.nexla.connector.config.vault.CredentialsStore;
import java.io.File;
import java.io.FileOutputStream;
import java.net.InetAddress;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.ToString;
import org.apache.commons.io.IOUtils;
import org.burningwave.tools.net.DefaultHostResolver;
import org.burningwave.tools.net.HostResolutionRequestInterceptor;
import org.burningwave.tools.net.MappedHostResolver;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SshTunnel implements AutoCloseable {
  private static final Logger LOGGER = LoggerFactory.getLogger(SshTunnel.class);
  public static final Pattern IPV4_PATTERN =
      Pattern.compile("^((25[0-5]|(2[0-4]|1\\d|[1-9]|)\\d)\\.?\\b){4}$");

  private static final int BACKGROUND_CHECK_INTERVAL_SEC = 60;
  private static final int SSH_SERVER_ALIVE_EVENTS_INTERVAL_MS =
      30_000; // SSH session configuration: the timeout interval in milliseconds before sending a
  // server alive message, if no message is received from the server.
  private static int SSH_CHANNEL_CHECK_TIMEOUT_MS; // The default is ~20 seconds

  // TODO here we have potential vulnerability because of reusing ssh tunnels (and JSch objects with
  // added private keys)
  private static final ConcurrentHashMap<SshTunnelStep, JSch> JSCH_MAP = new ConcurrentHashMap<>();
  private static final ConcurrentHashMap<SshTunnelStep, Pair<Integer, SshTunnelContext>>
      SSH_TUNNELS = new ConcurrentHashMap<>();

  private static final ScheduledExecutorService EXECUTOR_SERVICE =
      Executors.newScheduledThreadPool(
          1,
          new ThreadFactoryBuilder()
              .setNameFormat("ssh-tunnel-scheduled-pool-%d")
              .setDaemon(true)
              .build());

  private static final MappedHostResolverVerbose MAPPED_HOST_RESOLVER =
      new MappedHostResolverVerbose(Collections.emptyMap());

  static {
    try {
      // Used to redirect requests to target host
      // (https://dev.to/jjbrt/how-to-configure-hostname-resolution-to-use-a-universal-custom-hostname-resolver-in-java-14p0)
      HostResolutionRequestInterceptor.INSTANCE.install(
          MAPPED_HOST_RESOLVER, DefaultHostResolver.INSTANCE);
    } catch (Exception e) {
      LOGGER.error("Critical! Failed to configure custom MappedHostResolver", e);
    }
    try {
      SSH_CHANNEL_CHECK_TIMEOUT_MS =
          ofNullable(System.getenv("SSH_CHANNEL_CHECK_TIMEOUT_MS"))
              .map(Integer::parseInt)
              .orElse(120_000);
      LOGGER.info("SSH_CHANNEL_CHECK_TIMEOUT_MS is set to {}", SSH_CHANNEL_CHECK_TIMEOUT_MS);
    } catch (Exception e) {
      LOGGER.error("Failed to parse SSH_CHANNEL_CHECK_TIMEOUT_MS, using the default value", e);
      SSH_CHANNEL_CHECK_TIMEOUT_MS = 120_000;
    }
    LOGGER.info("SSH_CHANNEL_CHECK_TIMEOUT_MS configured to {}ms", SSH_CHANNEL_CHECK_TIMEOUT_MS);
    EXECUTOR_SERVICE.scheduleWithFixedDelay(
        () -> {
          try (TimeLogger ignored = new TimeLogger(LOGGER, "Check SSH tunnels")) {
            logStaticState();
            recoverDeadTunnels();
          } catch (Exception e) {
            LOGGER.error("Unexpected exception during SSH tunnels check", e);
          }
        },
        BACKGROUND_CHECK_INTERVAL_SEC,
        BACKGROUND_CHECK_INTERVAL_SEC,
        TimeUnit.SECONDS);
  }

  private final SshTunnelStep tunnelStep;
  private boolean isClosed = false;

  @SneakyThrows
  public SshTunnel(
      CredentialsStore credentialsStore,
      List<String> vaultKeys,
      SshTunnelConfig config,
      List<HostPort> remoteHosts) {
    this.tunnelStep =
        new SshTunnelStep(config.bastionUser, config.bastionHost, config.bastionPort, remoteHosts);
    JSch jSch = JSCH_MAP.computeIfAbsent(tunnelStep, sshTunnelStep -> newJSch());
    for (String key : vaultKeys) {
      Map<String, String> sshSettings =
          credentialsStore
              .getValuesMap(key)
              .orElseThrow(
                  () ->
                      new Exception(
                          String.format(
                              "SSH key %s is absent in %s storage, identity file was not added",
                              key, credentialsStore.getType())));
      addPrivateKey(jSch, sshSettings);
    }
    SSH_TUNNELS.compute(
        tunnelStep,
        (ignored, ctx) ->
            ctx == null
                ? Pair.with(1, create(jSch, tunnelStep, Collections.emptyMap()))
                : Pair.with(ctx.getValue0() + 1, ctx.getValue1()));
    restartIfDead(tunnelStep);
    if (!MAPPED_HOST_RESOLVER.verifyAllMappingApplied(Duration.ofMinutes(1))) {
      LOGGER.error(
          "Host mapping was not verified: Expected: {}, Actual: {}",
          MAPPED_HOST_RESOLVER.getAllMappedHosts(),
          MAPPED_HOST_RESOLVER.getActualHostMapping());
    }
  }

  /** Returns active port forwarding mapping as a Map<remoteHostPort, localPort> */
  public Map<HostPort, Integer> gePortForwarding() {
    if (isClosed) {
      throw new IllegalStateException("SSH tunnel has been closed");
    }
    return SSH_TUNNELS.get(tunnelStep).getValue1().remoteToLocalPort;
  }

  private static JSch newJSch() {
    JSch jSch = new JSch();
    Hashtable<String, String> props = new Hashtable<>();
    props.put("StrictHostKeyChecking", "no");
    jSch.setConfig(props);
    return jSch;
  }

  @SneakyThrows
  private void addPrivateKey(JSch jSch, Map<String, String> sshSettings) {
    String pk = sshSettings.get("privateKey");
    String passphrase = sshSettings.get("passphrase");
    File tempFile = File.createTempFile("ssh", "pkey");
    try {
      IOUtils.write(pk, new FileOutputStream(tempFile), StandardCharsets.UTF_8);
      jSch.addIdentity(tempFile.getAbsolutePath(), passphrase);
    } finally {
      tempFile.delete();
    }
  }

  private static void recoverDeadTunnels() {
    SSH_TUNNELS.forEach(
        (sshTunnelStep, tunnel) -> {
          try {
            if (tunnel != null) {
              restartIfDead(sshTunnelStep);
            }
          } catch (Exception e) {
            LOGGER.error("Failed to recover dead tunnel {}", sshTunnelStep, e);
          }
        });
  }

  private static void restartIfDead(SshTunnelStep tunnelStep) {
    Optional<SshTunnelContext> maybeTunnelCtx =
        Optional.ofNullable(SSH_TUNNELS.get(tunnelStep)).map(Pair::getValue1);
    if (maybeTunnelCtx.isEmpty()) {
      LOGGER.debug("No ssh session to check by tunnelStep: {}{}", tunnelStep, getThreadLogSuffix());
      return;
    }
    if (!isAlive(maybeTunnelCtx.get())) {
      SSH_TUNNELS.computeIfPresent(
          tunnelStep,
          (ignored, tunnel) -> {
            SshTunnelContext ctx = tunnel.getValue1();
            if (ctx.session.isConnected()
                && System.identityHashCode(ctx) != System.identityHashCode(maybeTunnelCtx.get())) {
              LOGGER.debug(
                  "Skip recovery for connected SSH session: {} (current={}, isAlive done for={}){}",
                  ctx,
                  System.identityHashCode(maybeTunnelCtx.get()),
                  System.identityHashCode(ctx),
                  getThreadLogSuffix());
              return tunnel;
            }
            LOGGER.warn(
                "SSH session is dead (connected={}), attempting to restart {}{}",
                ctx.session.isConnected(),
                tunnelStep,
                getThreadLogSuffix());
            JSch jSch = JSCH_MAP.get(tunnelStep);
            if (jSch == null) {
              throw new IllegalStateException("JSch object was not found for " + tunnelStep);
            }
            closePreviousConnection(tunnelStep, ctx);
            return Pair.with(tunnel.getValue0(), create(jSch, tunnelStep, ctx.remoteToLocalPort));
          });
    }
  }

  // Method could take up to SSH_CHANNEL_CHECK_TIMEOUT_MS seconds to execute
  private static boolean isAlive(SshTunnelContext context) {
    try {
      if (!context.session.isConnected()) {
        return false;
      }
      ChannelExec testChannel = (ChannelExec) context.session.openChannel("exec");
      testChannel.setCommand("true");
      testChannel.connect(SSH_CHANNEL_CHECK_TIMEOUT_MS);
      testChannel.disconnect();
      return true;
    } catch (Throwable t) {
      LOGGER.warn(
          "SSH session alive check failed: {}. Session connected={}{}",
          t.getMessage(),
          context.session.isConnected(),
          getThreadLogSuffix());
      return false;
    }
  }

  private static void closePreviousConnection(
      SshTunnelStep sshTunnelStep, SshTunnelContext context) {
    try {
      context.session.disconnect();
    } catch (Throwable t) {
      LOGGER.error("Could not close SSH connection: {}{}", sshTunnelStep, getThreadLogSuffix(), t);
    }
  }

  @SneakyThrows
  private static synchronized SshTunnelContext create(
      JSch jSch, SshTunnelStep step, Map<HostPort, Integer> portForwarding) {
    try (TimeLogger ignored =
        new TimeLogger(LOGGER, "Create SSH tunnel: " + step + getThreadLogSuffix())) {
      Session session = jSch.getSession(step.user, step.host, step.port);
      session.setServerAliveInterval(SSH_SERVER_ALIVE_EVENTS_INTERVAL_MS);
      session.connect(SSH_CHANNEL_CHECK_TIMEOUT_MS);

      // Map <RemoteHostPort, LocalPort>
      Map<HostPort, Integer> newPortForwarding = Maps.newHashMap();
      for (HostPort remoteHost : step.remoteHosts) {
        Integer localPort =
            portForwarding.getOrDefault(
                remoteHost, 0); // If lport is 0, the tcp port will be allocated.
        int allocatedLocalPort =
            session.setPortForwardingL(localPort, remoteHost.getHost(), remoteHost.getPort());
        newPortForwarding.put(remoteHost, allocatedLocalPort);
        if (!IPV4_PATTERN.matcher(remoteHost.getHost()).matches()) {
          MAPPED_HOST_RESOLVER.putHost(remoteHost.getHost(), "127.0.0.1");
        }
      }
      SshTunnelContext sshTunnelContext = new SshTunnelContext(newPortForwarding, session);
      LOGGER.info(
          "SSH tunnel established: sshTunnelStep={}, portForwarding={}{}",
          step,
          newPortForwarding,
          getThreadLogSuffix());
      return sshTunnelContext;
    }
  }

  public void close() {
    if (isClosed) {
      LOGGER.warn("SSH tunnel has been closed already", new Exception("Stack trace"));
      return;
    }
    SSH_TUNNELS.compute(
        tunnelStep,
        (ignored, ctx) -> {
          if (ctx == null) {
            return null;
          }
          if (ctx.getValue0() == 1) {
            closePreviousConnection(tunnelStep, ctx.getValue1());
            LOGGER.info("SSH tunnel closed: {}: {}", tunnelStep, ctx);
            return null;
          } else {
            LOGGER.info(
                "SSH tunnel preserved on closure ({}): {}: {}",
                ctx.getValue0() - 1,
                tunnelStep,
                ctx.getValue1());
            return Pair.with(ctx.getValue0() - 1, ctx.getValue1());
          }
        });

    // Here we don't expect the same host being accessed directly and through the SSH tunnel at the
    // same time by different credentials
    boolean hasNoOpenTunnelsToHost =
        SSH_TUNNELS.keySet().stream()
            .noneMatch(sshTunnelStep -> Objects.equals(sshTunnelStep.host, tunnelStep.host));
    if (hasNoOpenTunnelsToHost) {
      MAPPED_HOST_RESOLVER.removeHost(tunnelStep.host);
    }

    this.isClosed = true;
  }

  public static void logStaticState() {
    int portsForwarded =
        SSH_TUNNELS.values().stream()
            .mapToInt(objects -> objects.getValue1().remoteToLocalPort.size())
            .sum();
    LOGGER.info(
        "SSH tunnels: JSCH objects={}, SSH tunnels={}, total ports forwarded={}, SSH_TUNNELS={},"
            + " ALL_MAPPED_HOSTS={}",
        JSCH_MAP.size(),
        SSH_TUNNELS.size(),
        portsForwarded,
        SSH_TUNNELS,
        MAPPED_HOST_RESOLVER.getAllMappedHosts());
  }

  private static String getThreadLogSuffix() {
    Thread thread = Thread.currentThread();
    return "[thread=" + thread.getName() + "#" + thread.getId() + "]";
  }

  @AllArgsConstructor
  @ToString
  private static class SshTunnelContext {
    Map<HostPort, Integer> remoteToLocalPort;
    Session session;
  }

  static class MappedHostResolverVerbose extends MappedHostResolver {

    public MappedHostResolverVerbose(Map<String, String> hostAliases) {
      super(hostAliases);
    }

    /** Returns the expected host mapping for JVM */
    public Map<String, String> getAllMappedHosts() {
      return new HashMap<>(hostAliases);
    }

    /** Returns the actual host mapping used by JVM */
    public Map<String, String> getActualHostMapping() {
      return getAllMappedHosts().entrySet().stream()
          .collect(
              Collectors.toMap(
                  Map.Entry::getKey,
                  entry -> {
                    try {
                      return InetAddress.getByName(entry.getKey()).getHostAddress();
                    } catch (Exception e) {
                      LOGGER.error("Failed to check InetAddress for {}", entry.getKey(), e);
                      return e.getMessage();
                    }
                  }));
    }

    /**
     * Verify that all the expected mappings are applied and used by JVm (IP addresses could be
     * cached in JVM)
     */
    public boolean verifyAllMappingApplied(Duration timeout) {
      Map<String, String> expectedHostMapping = getAllMappedHosts();
      if (expectedHostMapping.isEmpty()) {
        return true;
      }
      long tsThreshold = System.currentTimeMillis() + timeout.toMillis();
      try (TimeLogger ignored = new TimeLogger(LOGGER, "Check all host mapping applied")) {
        LOGGER.info("Verify expected hosts mapping: {}", expectedHostMapping);
        return expectedHostMapping.entrySet().stream()
            .allMatch(
                entry -> {
                  try {
                    String expectedAddress = entry.getValue();
                    while (System.currentTimeMillis() < tsThreshold) {
                      String actualAddress = InetAddress.getByName(entry.getKey()).getHostAddress();
                      if (Objects.equals(expectedAddress, actualAddress)) {
                        return true;
                      }
                      Thread.sleep(500);
                    }
                    LOGGER.error("Failed to check InetAddress for {}: timed out", entry.getKey());
                    return false;
                  } catch (Exception e) {
                    LOGGER.error("Failed to check InetAddress for {}", entry.getKey(), e);
                    return false;
                  }
                });
      }
    }
  }
}
