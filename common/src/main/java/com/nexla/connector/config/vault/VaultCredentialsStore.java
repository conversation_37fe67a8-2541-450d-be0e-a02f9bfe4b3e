package com.nexla.connector.config.vault;

import static java.util.Optional.empty;
import static java.util.Optional.of;

import com.bettercloud.vault.Vault;
import com.bettercloud.vault.VaultConfig;
import com.bettercloud.vault.VaultException;
import com.github.rholder.retry.*;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class VaultCredentialsStore implements CredentialsStore {

  public static final String TYPE = "VAULT";
  public static final int ATTEMPT_NUMBER = 5;
  private static final Logger LOGGER = LoggerFactory.getLogger(VaultCredentialsStore.class);

  public static final RetryListener RETRY_LISTENER =
      new RetryListener() {
        @Override
        public <V> void onRetry(Attempt<V> attempt) {
          if (attempt.hasException()) {
            LOGGER.error(
                "Attempt {} (of {}) resulted in error",
                attempt.getAttemptNumber(),
                ATTEMPT_NUMBER,
                attempt.getExceptionCause());
          }
        }
      };

  public static final Retryer<Optional<Map<String, String>>> RETRYER =
      RetryerBuilder.<Optional<Map<String, String>>>newBuilder()
          .withWaitStrategy(WaitStrategies.fixedWait(1000, TimeUnit.MILLISECONDS))
          .withStopStrategy(StopStrategies.stopAfterAttempt(ATTEMPT_NUMBER))
          .withRetryListener(RETRY_LISTENER)
          .retryIfException()
          .build();

  private final Vault vault;

  @SneakyThrows
  public VaultCredentialsStore(String url, String token) {
    VaultConfig build = new VaultConfig().address(url).token(token).build();

    build.getSslConfig().verify(false).build();

    this.vault = new Vault(build);
  }

  @SneakyThrows
  public Optional<Map<String, String>> getValuesMap(String path) {
    return RETRYER.call(
        () -> {
          try {
            return of(vault.logical().read(path).getData());
          } catch (VaultException e) {
            if (e.getHttpStatusCode() == 404) {
              return empty();
            }
            throw e;
          }
        });
  }

  @Override
  public String getType() {
    return TYPE;
  }

  @SneakyThrows
  public void setValuesMap(String path, Map<String, Object> values) {
    vault.logical().write(path, values);
  }
}
