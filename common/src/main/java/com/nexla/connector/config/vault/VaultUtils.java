package com.nexla.connector.config.vault;

import static com.nexla.common.NexlaConstants.*;
import static java.util.Optional.ofNullable;

import com.nexla.connector.config.BaseConnectorConfig;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

public interface VaultUtils {

  static String toEnvVar(String str) {
    return StringUtils.replace(str.toUpperCase(), ".", "_");
  }

  static CredentialsStore createNexlaCredentialsStore(Map<String, String> props) {
    String credSource = envOrProp("CREDENTIALS_SOURCE", props, "LOCAL");

    switch (credSource) {
      case AmazonCredentialsStore.TYPE:
        return new AmazonCredentialsStore(
            envOrProp(toEnvVar(AWS_SECRET_MANAGER_REGION), props, null),
            envOrProp(toEnvVar(AWS_SECRET_MANAGER_ACCESS_KEY), props, null),
            envOrProp(toEnvVar(AWS_SECRET_MANAGER_SECRET_KEY), props, null),
            envOrProp(toEnvVar(AWS_SECRET_MANAGER_ROLE_ARN), props, null),
            envOrProp(toEnvVar(AWS_SECRET_MANAGER_IDENTITY_TOKEN_FILE), props, null),
            envOrProp(toEnvVar(AWS_SECRET_MANAGER_EXTERNAL_ID), props, null));
      case VaultCredentialsStore.TYPE:
        return new VaultCredentialsStore(
            envOrProp(toEnvVar(BaseConnectorConfig.VAULT_HOST), props, null),
            envOrProp(toEnvVar(BaseConnectorConfig.VAULT_TOKEN), props, null));
      case GoogleCredentialsStore.TYPE:
        return new GoogleCredentialsStore(
            envOrProp(toEnvVar(GCP_SECRET_MANAGER_PROJECT_ID), props, null),
            envOrProp(toEnvVar(GCP_SECRET_MANAGER_SERVICE_ACCOUNT_KEY), props, null));
      case K8sCredentialsStore.TYPE:
        return new K8sCredentialsStore(
            envOrProp(
                toEnvVar(K8S_SECRETS_BASE_PATH), props, K8sCredentialsStore.WELL_KNOWN_PLACE));
      default:
        return new LocalCredentialsStore();
    }
  }

  static String envOrProp(String name, Map<String, String> props, String defaultValue) {
    return ofNullable(System.getenv().get(name))
        .orElseGet(
            () -> ofNullable(props.get(name.toLowerCase().replace("_", "."))).orElse(defaultValue));
  }
}
