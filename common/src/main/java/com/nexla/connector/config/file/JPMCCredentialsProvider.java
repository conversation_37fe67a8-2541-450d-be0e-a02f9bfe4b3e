package com.nexla.connector.config.file;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.util.Base64;
import java.util.Map;
import lombok.SneakyThrows;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.parser.Parser;
import org.jsoup.select.Elements;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.AssumeRoleWithSamlRequest;
import software.amazon.awssdk.services.sts.model.AssumeRoleWithSamlResponse;
import software.amazon.awssdk.services.sts.model.Credentials;

public class JPMCCredentialsProvider {

  public static final String JPMC_AUTH_TYPE = "JPMC";

  private final AWSAuthConfig authConfig;
  private final String domain;
  private final String principal;
  private final String credential;

  public JPMCCredentialsProvider(AWSAuthConfig authConfig) {
    this.authConfig = authConfig;
    this.domain = authConfig.customAuthProps.get("domain");
    this.principal = authConfig.customAuthProps.get("principal");
    this.credential = authConfig.customAuthProps.get("credential");
  }

  @SneakyThrows
  public AwsCredentialsProvider getCredentialsProvider() {
    String samlAssertion;
    String userName = domain + "\\" + principal;

    URL adfsUrl =
        new URL(
            "https://ida.jpmorganchase.com/adfs/ls/idpInitiatedSignOn.aspx?loginToRp=urn:amazon:webservices&RequestedAuthenticationContext=urn:oasis:names:tc:SAML:2.0:ac:classes:Password");
    Connection.Response initialResp =
        Jsoup.connect(adfsUrl.toString()).followRedirects(false).execute();
    Document loginForm = initialResp.parse();
    Map<String, String> cookies = initialResp.cookies();

    Element loginElement = loginForm.getElementById("loginForm");
    if (loginElement == null) {
      throw new IOException("Unable to get the ADFS login form");
    } else {
      URL loginPostUrl = new URL("https", adfsUrl.getHost(), loginElement.attr("action"));
      cookies.clear();
      Connection conn = Jsoup.connect(loginPostUrl.toString());
      Connection.Response SamlResp =
          conn.data("UserName", userName)
              .data("Kmsi", "false")
              .data("Password", credential)
              .method(Connection.Method.POST)
              .execute();
      Document SamlDoc = SamlResp.parse();
      Elements samlElements = SamlDoc.getElementsByAttributeValue("name", "SAMLResponse");
      if (samlElements.isEmpty()) {
        throw new IOException("Invalid SAML response received");
      } else {
        Element samlElement = samlElements.first();
        samlAssertion = samlElement.val();
      }
    }
    StsClient stsClient = StsClient.builder().region(Region.of(authConfig.region)).build();

    AssumeRoleWithSamlRequest assumeRoleRequest =
        AssumeRoleWithSamlRequest.builder()
            .roleArn(authConfig.arn)
            .principalArn(extractPrincipalArnFromSAMLAssertion(samlAssertion, authConfig.arn))
            .durationSeconds(3600)
            .samlAssertion(samlAssertion)
            .build();

    AssumeRoleWithSamlResponse assumeRoleWithSAMLResult =
        stsClient.assumeRoleWithSAML(assumeRoleRequest);
    Credentials credentials = assumeRoleWithSAMLResult.credentials();

    return StaticCredentialsProvider.create(
        AwsSessionCredentials.create(
            credentials.accessKeyId(), credentials.secretAccessKey(), credentials.sessionToken()));
  }

  private String extractPrincipalArnFromSAMLAssertion(String samlAssertion, String roleArn)
      throws UnsupportedEncodingException {
    String principalArn = null;
    String assertion = new String(Base64.getDecoder().decode(samlAssertion), "UTF-8");
    Document assertionDoc = Jsoup.parse(assertion, "UTF-8", Parser.xmlParser());
    Elements samlAttribute =
        assertionDoc.getElementsByAttributeValue(
            "name", "https://aws.amazon.com/SAML/Attributes/Role");
    for (Element attribute : samlAttribute) {
      Elements samlAttrValues = attribute.getElementsByTag("AttributeValue");
      for (Element samlRoleAttr : samlAttrValues) {
        String samlRole = samlRoleAttr.text();
        if (samlRole.contains(roleArn)) {
          principalArn = samlRole.split(",")[0];
        }
      }
    }
    return principalArn;
  }
}
