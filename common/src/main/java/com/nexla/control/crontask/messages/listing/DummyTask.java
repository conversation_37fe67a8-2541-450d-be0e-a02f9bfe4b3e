package com.nexla.control.crontask.messages.listing;

import com.nexla.control.crontask.NexlaTaskType;
import com.nexla.control.crontask.TaskRequestMessage;
import lombok.Data;

@Data
public class DummyTask implements TaskRequestMessage {

  @Override
  public NexlaTaskType getTaskType() {
    return NexlaTaskType.DUMMY;
  }

  private final String messageId;
  private final Long timestamp;
  private final Long createdAt = System.currentTimeMillis();
}
