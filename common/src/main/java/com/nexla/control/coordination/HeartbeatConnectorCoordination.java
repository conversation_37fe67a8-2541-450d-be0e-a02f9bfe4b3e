package com.nexla.control.coordination;

import com.nexla.common.ResourceType;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

@Data
public class HeartbeatConnectorCoordination implements CoordinationMessage {

  private final String messageId;
  private final ResourceType resourceType;
  private final Integer resourceId;
  private final Long runId;
  private final HeartbeatType type;
  private final Long timeMs;
  private final Long createdAt = System.currentTimeMillis();

  // Context may contain the next data
  // flowType - for inmemory flow (com.nexla.connector.config.FlowType)
  // flowId   - for inmemory flow (int)
  // sourceId - for inmemory flow (int)
  // state    - for inmemory flow(PipelineRunState)
  private final Map<String, String> context = new HashMap<>();

  @Override
  public CoordinationEventType getCoordinationEventType() {
    return CoordinationEventType.HEARTBEAT_CONNECTOR;
  }
}
