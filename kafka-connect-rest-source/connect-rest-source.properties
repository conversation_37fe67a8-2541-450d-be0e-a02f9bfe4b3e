name=rest-source

# Match it up with the environment where the connector is defined
credentials.source=test

bootstrap.servers=localhost:9092
dataset.replication=1
source_id=REPLACE_ME

# Many types available, replace with appropriate type
source_type=REPLACE_ME

# Find this in the connector ConfigDef, or otherwise in the connector repo. It should be the class that extends BaseSourceConnector or BaseSinkConnector
connector.class=com.nexla.connector.rest.source.RestSourceConnector

# Retrieve these from admin-api
credsEnc=REPLACE_ME
credsEncIv=REPLACE_ME

# Only required for sources
runId=1