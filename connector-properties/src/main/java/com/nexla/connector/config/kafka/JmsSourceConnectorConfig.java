package com.nexla.connector.config.kafka;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.SourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.ssh.tunnel.ConfigWithAuth;
import org.apache.kafka.common.config.ConfigDef;

import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_ONCE;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.Type.INT;
import static org.apache.kafka.common.config.ConfigDef.Type.STRING;

public class JmsSourceConnectorConfig extends SourceConnectorConfig implements ConfigWithAuth {

	private static final String GROUP = "jms";

	private static final String SOURCE_NAME = "source.name";
	private static final String PARALLELISM = "parallelism";
	private static final String JMS_SELECTOR = "jms.selector";
	private static final String SOURCE_TYPE = "source.type";
	private static final String PARSER_TYPE = "parser.type";
	private static final String SKIP_BYTES = "skip.bytes";

	public final JmsAuthConfig authConfig;
	public final Integer parallelism;
	public final String sourceName;
	public final Optional<String> selector;
	public final String sourceType;
	public final String parserType;
	public final int skipBytes;

	public JmsSourceConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);

		Map<String, String> authMap = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);
		this.authConfig = new JmsAuthConfig(authMap, credsId);

		this.sourceName = getString(SOURCE_NAME);
		this.parallelism = getInt(PARALLELISM);
		this.selector = opt(getString(JMS_SELECTOR));
		this.sourceType = getString(SOURCE_TYPE);
		this.parserType = getString(PARSER_TYPE);
		this.skipBytes = getInt(SKIP_BYTES);
	}

	public static NexlaConfigDef configDef() {

		NexlaConfigDef baseConfig = sourceConfigDef();

		return new NexlaConfigDef(baseConfig)
			.withKey(nexlaKey(SOURCE_NAME, STRING, null)
				.documentation("Destination topic")
				.group(GROUP)
				.displayName("Destination topic to publish data to"))

			.withKey(nexlaKey(PARALLELISM, INT, 1)
				.documentation("Parallelism level for JMS source")
				.group(GROUP)
				.displayName("Parallelism level for JMS source"))

			.withKey(nexlaKey(JMS_SELECTOR, STRING, null)
				.documentation("JMS selector")
				.group(GROUP)
				.displayName("JMS selector"))

			.withKey(nexlaKey(SOURCE_TYPE, STRING, "topic")
				.documentation("Target type: [topic, queue]")
				.group(GROUP)
				.displayName("Target type: [topic, queue]"))

			.withKey(nexlaKey(PARSER_TYPE, STRING, "json")
				.documentation("Data format")
				.group(GROUP)
				.displayName("Data format"))

			.withKey(nexlaKey(SKIP_BYTES, INT, 0)
				.documentation("Amount of bytes to skip")
				.group(GROUP)
				.displayName("Amount of bytes to skip"))

			// Override for parent default value. For streaming sources default should be true.
			.withKeyOverrideExisting(nexlaKey(SCHEMA_DETECTION_ONCE, ConfigDef.Type.BOOLEAN, true)
				.importance(LOW)
				.documentation("Schema detection: detect schema once")
				.group(CONNECTOR_GROUP)
				.displayName("Schema detection: detect schema once"))

			;

	}

	@Override
	public BaseAuthConfig authConfig() {
		return authConfig;
	}
}
