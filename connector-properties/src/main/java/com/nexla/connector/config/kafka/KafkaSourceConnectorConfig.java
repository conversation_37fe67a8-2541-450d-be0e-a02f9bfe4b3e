package com.nexla.connector.config.kafka;

import com.bazaarvoice.jolt.JsonUtils;
import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.NexlaConfigKey;
import com.nexla.connector.config.SourceConnectorConfig;
import org.apache.kafka.common.config.ConfigDef;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

import static com.nexla.common.ConfigUtils.opt;
import static com.nexla.common.NexlaDataCredentials.getCreds;
import static com.nexla.common.parse.ParserConfigs.SchemaDetection.SCHEMA_DETECTION_ONCE;
import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static org.apache.kafka.common.config.ConfigDef.Importance.LOW;
import static org.apache.kafka.common.config.ConfigDef.Type.*;

public class KafkaSourceConnectorConfig extends SourceConnectorConfig {

	private static final String KAFKA_GROUP = "kafka";
	private static final String DEFAULT_KEY_SER = "org.apache.kafka.common.serialization.StringDeserializer";
	private static final String DEFAULT_VALUE_SER = "org.apache.kafka.common.serialization.StringDeserializer";

	private static final String KEY_DESERIALIZER = "key.deserializer";
	private static final String VALUE_DESERIALIZER = "value.deserializer";

	private static final String TOPIC_NAME = "topic";
	private static final String OFFSET_MODE = "offset.mode";
	private static final String PARSER_TYPE = "parser.type";
	private static final String SERIALIZATION_VARIANT = "serialization.variant";
	private static final String SCHEMA = "schema";
	private static final String SCHEMA_REGISTRY_URL = "schema.registry.url";
	private static final String SCHEMA_REGISTRY_ALLOW_UNTRUSTED = "schema.registry.allowUntrusted";
	private static final String SCHEMA_REGISTRY_API_KEY = "schema.registry.api.key";
	private static final String SCHEMA_REGISTRY_API_SECRET = "schema.registry.api.secret";
	private static final String START_FROM_DATE = "start.from.date";
	private static final String START_FROM_OFFSETS = "start.from.offsets";
	private static final String PARALLELISM = "parallelism";
	private static final String SCHEMA_DETECTION_CONFLUENT_CACHE_TTL_MINUTES = "schema.detection.confluent.cache.ttl";

	public final KafkaOffsetMode offsetMode;
	public final String topic;
	public final String keyDeser;
	public final String valueDeser;
	public final KafkaAuthConfig authConfig;
	public final String parserType;
	public final String sourceSerializationVariant;
	public final Optional<Long> startOffsetTimestamp;
	public final Optional<Map<String, Object>> startOffsetManual;
	public final Integer parallelism;
	public final String schema;
	public final String schemaRegistryUrl;
	public final boolean schemaRegistryAllowUntrusted;
	public final Integer schemaDetectionConfluentCacheTTLMinutes; 

	public final String schemaRegistryApiKey;
	public final String schemaRegistryApiSecret;

	public KafkaSourceConnectorConfig(Map<String, String> originals) {
		super(configDef(), originals);
		this.offsetMode = KafkaOffsetMode.fromString(getString(OFFSET_MODE));

		Map<String, String> authMap = unitTest ? originals : getCreds(decryptKey, credsEnc, credsEncIv);

		this.topic = getString(TOPIC_NAME);
		this.keyDeser = getString(KEY_DESERIALIZER);
		this.valueDeser = getString(VALUE_DESERIALIZER);
		this.authConfig = new KafkaAuthConfig(authMap, credsId);
		this.parserType = getString(PARSER_TYPE);
		this.schema = getString(SCHEMA);
		this.schemaRegistryUrl = getString(SCHEMA_REGISTRY_URL);
		this.schemaRegistryAllowUntrusted = getBoolean(SCHEMA_REGISTRY_ALLOW_UNTRUSTED);
		this.schemaRegistryApiKey = getString(SCHEMA_REGISTRY_API_KEY);
		this.schemaRegistryApiSecret = getString(SCHEMA_REGISTRY_API_SECRET);
		this.sourceSerializationVariant = getString(SERIALIZATION_VARIANT);
		this.startOffsetTimestamp = opt(getString(START_FROM_DATE))
			.map(DateTime::parse)
			.map(DateTime::toDate)
			.map(Date::getTime);
		this.startOffsetManual = opt(getString(START_FROM_OFFSETS))
				.map(JsonUtils::jsonToMap);
		this.parallelism = getInt(PARALLELISM);
		this.schemaDetectionConfluentCacheTTLMinutes = getInt(SCHEMA_DETECTION_CONFLUENT_CACHE_TTL_MINUTES);
	}

	public static NexlaConfigDef configDef() {

		NexlaConfigDef baseConfig = sourceConfigDef();

		return new NexlaConfigDef(baseConfig)
			.withKey(nexlaKey(KEY_DESERIALIZER, STRING, DEFAULT_KEY_SER)
				.documentation("Key Deserializer")
				.group(KAFKA_GROUP)
				.displayName("Key deserializer for reading keys from Kafka topic"))

			.withKey(nexlaKey(VALUE_DESERIALIZER, STRING, DEFAULT_VALUE_SER)
				.documentation("Value Deserializer")
				.group(KAFKA_GROUP)
				.displayName("Value deserializer for reading from Kafka topic"))

			.withKey(nexlaKey(TOPIC_NAME, STRING, null)
				.documentation("Topic")
				.group(KAFKA_GROUP)
				.displayName("Kafka topic name"))

			.withKey(nexlaKey(SCHEMA, STRING, null)
				.documentation("Schema")
				.group(KAFKA_GROUP)
				.displayName("Kafka message schema"))

			.withKey(nexlaKey(SCHEMA_REGISTRY_URL, STRING, null)
				.documentation("Schema Registry URL")
				.group(KAFKA_GROUP)
				.displayName("Schema Registry URL"))

			.withKey(nexlaKey(SCHEMA_REGISTRY_ALLOW_UNTRUSTED, BOOLEAN, false)
				.documentation("Schema Registry allow untrusted")
				.group(KAFKA_GROUP)
				.displayName("Schema Registry allow untrusted"))

			.withKey(nexlaKey(SCHEMA_REGISTRY_API_KEY, STRING, null)
				.documentation("Schema Registry API Key")
				.group(KAFKA_GROUP)
				.displayName("Schema Registry API Key"))

			.withKey(nexlaKey(SCHEMA_REGISTRY_API_SECRET, STRING, null)
				.documentation("Schema Registry API Secret")
				.group(KAFKA_GROUP)
				.displayName("Schema Registry API Secret"))

			.withKey(nexlaKey(OFFSET_MODE, STRING, "earliest")
				.documentation("Default Kafka offset mode")
				.group(KAFKA_GROUP)
				.displayName("Default Kafka offset mode"))

			.withKey(nexlaKey(SERIALIZATION_VARIANT, STRING, "none")
				.documentation("Data format")
				.group(KAFKA_GROUP)
				.displayName("Used to specify how the source data was serialized"))

			.withKey(nexlaKey(PARSER_TYPE, STRING, "json")
				.documentation("Data format")
				.group(KAFKA_GROUP)
				.displayName("Data format for data on kafka topic"))

			.withKey(nexlaKey(START_FROM_DATE, STRING, null)
				.documentation("Consume Data from")
				.group(KAFKA_GROUP)
				.displayName("Start consuming data from this date")
				.dependsOn(new NexlaConfigKey.DependsOn("offset.mode", "from_date")))

			.withKey(nexlaKey(START_FROM_OFFSETS, STRING, null)
				.documentation("Start reading from particular offsets (JSON)")
				.group(KAFKA_GROUP)
				.displayName("Start reading from particular offsets (JSON)"))

			.withKey(nexlaKey(PARALLELISM, INT, 3)
				.documentation("Parallelism level for kafka source")
				.group(KAFKA_GROUP)
				.displayName("Parallelism level for kafka source"))

			// Override for parent default value. For streaming sources default should be true.
			.withKeyOverrideExisting(nexlaKey(SCHEMA_DETECTION_ONCE, ConfigDef.Type.BOOLEAN, true)
				.importance(LOW)
				.documentation("Schema detection: detect schema once")
				.group(CONNECTOR_GROUP)
				.displayName("Schema detection: detect schema once"))

			.withKey(nexlaKey(SCHEMA_DETECTION_CONFLUENT_CACHE_TTL_MINUTES, INT, 60)
				.documentation("TTL for Schema detection confluent cache in minutes")
				.group(KAFKA_GROUP)
				.displayName("Schema detection confluent cache minutes"))

			;

	}

}
