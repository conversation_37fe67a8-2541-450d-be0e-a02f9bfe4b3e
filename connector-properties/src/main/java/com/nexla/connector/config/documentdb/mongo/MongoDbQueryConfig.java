package com.nexla.connector.config.documentdb.mongo;

import com.nexla.connector.config.NexlaConfigDef;
import com.nexla.connector.config.documentdb.DocumentDBQueryConfig;
import org.apache.kafka.common.config.ConfigDef;

import java.util.*;

import static com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey;
import static com.nexla.connector.config.documentdb.DocumentDbSourceConnectorConfig.DOCUMENT_DB_SOURCE_GROUP;
import static java.util.Optional.ofNullable;

public class MongoDbQueryConfig extends DocumentDBQueryConfig {

    public static final String MONGO_QUERY_EXPRESSION = "mongo.query.expression";
    public static final String MONGO_PROJECTION_EXPRESSION = "mongo.projection.expression";
    public static final String MONGO_SORT_EXPRESSION = "mongo.sort.expression";
    public static final String MONGO_LIMIT_COUNT = "mongo.limit.count";
    public static final String MONGO_SKIP_COUNT = "mongo.skip.count";

    public static final Integer MONGO_DEFAULT_LIMIT_COUNT = 5;
    public static final Integer MONGO_DEFAULT_SKIP_COUNT = 0;

    public final String queryExpression;
    public final Optional<String> projectionExpression;
    public final Optional<String> sortExpression;
    public final Integer limitCount;
    public final Integer skipCount;

    public MongoDbQueryConfig(Map<String, String> queryConfigMap) {
        super(mongoQueryConfigDef(), queryConfigMap);

        this.queryExpression = getString(MONGO_QUERY_EXPRESSION);
        this.projectionExpression = ofNullable(getString(MONGO_PROJECTION_EXPRESSION));
        this.sortExpression = ofNullable(getString(MONGO_SORT_EXPRESSION));
        this.limitCount = getInt(MONGO_LIMIT_COUNT);
        this.skipCount = getInt(MONGO_SKIP_COUNT);
    }

    public static NexlaConfigDef mongoQueryConfigDef() {
        return new NexlaConfigDef()
            .withKey(nexlaKey(MONGO_QUERY_EXPRESSION, ConfigDef.Type.STRING, null)
                .group(DOCUMENT_DB_SOURCE_GROUP)
                .documentation("Query expression for Mongo in JSON format"))
            .withKey(nexlaKey(MONGO_PROJECTION_EXPRESSION, ConfigDef.Type.STRING, null)
                .group(DOCUMENT_DB_SOURCE_GROUP)
                .documentation("Fields that should be included into query results"))
            .withKey(nexlaKey(MONGO_SORT_EXPRESSION, ConfigDef.Type.STRING, null)
                .group(DOCUMENT_DB_SOURCE_GROUP)
                .documentation("Sort expression for Mongo in JSON format"))
            .withKey(nexlaKey(MONGO_LIMIT_COUNT, ConfigDef.Type.INT, MONGO_DEFAULT_LIMIT_COUNT)
                .group(DOCUMENT_DB_SOURCE_GROUP)
                .documentation("Number of elements that should appear in query result"))
            .withKey(nexlaKey(MONGO_SKIP_COUNT, ConfigDef.Type.INT, MONGO_DEFAULT_SKIP_COUNT)
                .group(DOCUMENT_DB_SOURCE_GROUP)
                .documentation("Number of elements to skip in query result"));
    }

}
