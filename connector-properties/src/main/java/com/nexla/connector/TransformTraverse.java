package com.nexla.connector;

import com.google.common.collect.Lists;
import one.util.streamex.EntryStream;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static java.util.Optional.of;

public class TransformTraverse {

	public static final String DELIMITER = ".";

	private final Function<String, String> keyTransform;
	private final Function<Object, Object> valueTransform;

	public TransformTraverse(Function<String, String> keyTransform, Function<Object, Object> valueTransform) {
		this.keyTransform = keyTransform;
		this.valueTransform = valueTransform;
	}

	public LinkedHashMap<String, Object> transform(LinkedHashMap<String, Object> data) {
		return processMap(Optional.empty(), data);
	}

	private LinkedHashMap<String, Object> processMap(Optional<String> path, LinkedHashMap<String, Object> map) {
		LinkedHashMap<String, Object> result = new LinkedHashMap<>();
		EntryStream.of(map)
			.forKeyValue((k, elem) -> {
				String newPath = delimitedPath(path) + k;
				if (elem instanceof List) {
					List<Object> processed = processList(of(newPath), (List<Object>) elem);
					if (!processed.isEmpty()) {
						result.put(k, processed);
					}
				} else if (elem instanceof Map) {
					Map<String, Object> processed = processMap(of(newPath), (LinkedHashMap<String, Object>) elem);
					if (!processed.isEmpty()) {
						result.put(k, processed);
					}
				} else {
					result.put(keyTransform.apply(k), valueTransform.apply(elem));
				}
			});
		return result;
	}

	private String delimitedPath(Optional<String> path) {
		return path.map(x -> x + DELIMITER).orElse("");
	}

	private List<Object> processList(Optional<String> path, List<Object> list) {
		List<Object> result = Lists.newArrayList();
		for (int i = 0; i < list.size(); i++) {
			Object elem = list.get(i);
			if (elem instanceof List) {
				List<Object> processed = processList(path, (List<Object>) elem);
				if (!processed.isEmpty()) {
					result.add(processed);
				}
			} else if (elem instanceof Map) {
				Map<String, Object> processed = processMap(path, (LinkedHashMap<String, Object>) elem);
				if (!processed.isEmpty()) {
					result.add(processed);
				}
			} else {
				result.add(valueTransform.apply(elem));
			}
		}
		return result;
	}

}