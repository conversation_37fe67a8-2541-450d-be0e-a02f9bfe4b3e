package com.nexla.connector

import java.io.{EOFException, IOException}
import java.util.Optional
import com.fasterxml.jackson.core.JsonParseException
import com.fasterxml.jackson.core.io.JsonEOFException
import com.nexla.common.probe.ExceptionResolution
import com.nexla.common.probe.ExceptionResolution._
import com.nexla.common.{NexlaBucket, NexlaFile, ResourceType}
import com.nexla.connector.ConnectorService.AuthResponse
import com.nexla.connector.config.rest.BaseAuthConfig
import one.util.streamex.StreamEx
import org.apache.kafka.common.config.AbstractConfig
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class ConnectorServiceTest
  extends AnyFlatSpecLike
    with Matchers {

  class StorageSpecificException extends RuntimeException

  val service = new ConnectorService[BaseAuthConfig]() {

    override def initLogger(resourceType: ResourceType, resourceId: Integer, optional: Optional[Integer]): Unit = ???

    override def authenticate(authConfig: BaseAuthConfig): AuthResponse = ???

    override def listBuckets(config: AbstractConfig): StreamEx[NexlaBucket] = ???

    override def listBucketContents(config: AbstractConfig): StreamEx[NexlaFile] = ???

    override def checkWriteAccess(config: AbstractConfig): Boolean = ???

    override protected def findResolution(e: Throwable): Optional[ExceptionResolution] =
      e match {
        case _: StorageSpecificException => Optional.of(ExceptionResolution.RETRY)
        case _ => Optional.empty()
      }
  }

  it should "retry on IO" in {
    service.analyzeException(new IOException) shouldBe RETRY
  }

  it should "quarantine for jackson exceptions" in {
    service.analyzeException(new JsonEOFException(null, null, null)) shouldBe QUARANTINE
  }

  it should "retry for storage specific exception" in {
    service.analyzeException(new StorageSpecificException()) shouldBe RETRY
  }

  it should "retry for storage specific exception if it is root cause" in {
    service.analyzeException(new JsonParseException(null, "", new StorageSpecificException())) shouldBe RETRY
  }

  it should "skip for GZIP corrupted exception" in {
    service.analyzeException(new EOFException("Unexpected end of ZLIB input stream")) shouldBe SKIP_FILE
  }

  it should "skip for zip corrupted file" in {
    service.analyzeException(new Exception("unexpected EOF - expected len: 4212492 - actual len: 4212281")) shouldBe SKIP_FILE
  }
}
