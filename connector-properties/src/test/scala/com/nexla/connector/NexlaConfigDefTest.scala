package com.nexla.connector

import com.nexla.connector.config.NexlaConfigDef
import com.nexla.connector.config.NexlaConfigKeyBuilder.nexlaKey
import org.apache.kafka.common.config.ConfigDef
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class NexlaConfigDefTest
  extends AnyFlatSpecLike
    with Matchers {

  it should "parse to string" in {

    val cfg = new NexlaConfigDef()
      .withKey(
        nexlaKey("key1", ConfigDef.Type.STRING, "234")
          .notNullable())
      .withKey(
        nexlaKey("key2", ConfigDef.Type.STRING, "234")
          .notEmpty())

    cfg.parse(Map("key1" -> 123).asJava).get("key1") shouldEqual "123"
    cfg.parse(Map("key1" -> "123").asJava).get("key1") shouldEqual "123"
    cfg.parse(Map("key1" -> 123.0d).asJava).get("key1") shouldEqual "123.0"
    cfg.parse(Map("key1" -> null).asJava).get("key1") shouldEqual "234"
    cfg.parse(Map("key1" -> "").asJava).get("key1") shouldEqual ""
    cfg.parse(Map("key2" -> "").asJava).get("key2") shouldEqual "234"
  }

  it should "override key" in {
    val originalCfg = new NexlaConfigDef()
      .withKey(
        nexlaKey("key1", ConfigDef.Type.STRING, "1")
          .notNullable())
      .withKey(
        nexlaKey("key2", ConfigDef.Type.STRING, "2")
          .notEmpty())

    val overridenCfg = new NexlaConfigDef(originalCfg)
      .withKeyOverrideExisting(
        nexlaKey("key1", ConfigDef.Type.STRING, "11")
          .notNullable())

    overridenCfg.parse(Map("key1" -> null).asJava).get("key1") shouldEqual "11"
    overridenCfg.parse(Map("key2" -> null).asJava).get("key2") shouldEqual "2"
  }

  it should "parse to int" in {

    val cfg = new NexlaConfigDef()
      .withKey(
        nexlaKey("key1", ConfigDef.Type.INT, 234))

    cfg.parse(Map("key1" -> 123).asJava).get("key1") shouldEqual 123
    cfg.parse(Map("key1" -> "123").asJava).get("key1") shouldEqual 123
    cfg.parse(Map("key1" -> 123.0d).asJava).get("key1") shouldEqual 123
    cfg.parse(Map("key1" -> "trash").asJava).get("key1") shouldEqual 234
    cfg.parse(Map("key1" -> null).asJava).get("key1") shouldEqual null
  }

}
