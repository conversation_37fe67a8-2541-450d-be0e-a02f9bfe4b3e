package com.nexla.probe.sql;

import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.NexlaSchema;
import com.nexla.common.*;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.probe.ColumnInfo;
import com.nexla.common.probe.ProbeException;
import com.nexla.common.probe.ProbeSampleResultEntry;
import com.nexla.common.probe.SqlSampleResult;
import com.nexla.common.schema.SchemaType;
import com.nexla.connector.ConnectorService;
import com.nexla.connector.MessageReader;
import com.nexla.connector.config.MappingConfig;
import com.nexla.connector.config.jdbc.DbListTreeServicePaged;
import com.nexla.connector.config.jdbc.DbPage;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.JdbcSourceConnectorConfig;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.ssh.HostPort;
import com.nexla.connector.config.ssh.tunnel.SshTunnelSupport;
import com.nexla.connector.config.vault.CredentialsStore;
import com.nexla.connector.properties.SqlConfigAccessor;
import com.nexla.probe.sql.connection.ConnectionStrategy;
import com.nexla.probe.sql.dto.ListDataTypesResult;
import connect.data.Schema;
import connect.jdbc.sink.dialect.AutomaticBinding;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DialectRegistry;
import connect.jdbc.sink.dialect.NexlaDbInfo;
import connect.jdbc.source.BulkTableQuerier;
import connect.jdbc.source.TableQuerier;
import connect.jdbc.source.UpdateTableQuerier;
import connect.jdbc.util.JdbcUtils;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.AbstractConfig;
import org.javatuples.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static com.nexla.common.ConnectionType.DATABRICKS;
import static com.nexla.common.ConnectionType.NETSUITE_JDBC;
import static com.nexla.common.ConnectionType.REDSHIFT;
import static com.nexla.common.ConnectionType.SNOWFLAKE;
import static com.nexla.common.ConverterUtils.toLong;
import static com.nexla.common.FileUtils.closeSilently;
import static com.nexla.common.StreamUtils.lhm;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.MappingConfig.MODE_MANUAL;
import static com.nexla.connector.properties.SqlConfigAccessor.INSERT_MODE;
import static com.nexla.connector.properties.SqlConfigAccessor.PRIMARY_KEY;
import static com.nexla.probe.sql.RetriableSqlState.getRetriableSqlState;
import static com.nexla.probe.sql.TableListingUtils.*;
import static com.nexla.probe.sql.dto.InternalDto.Column;
import static com.nexla.probe.sql.dto.InternalDto.Table;
import static connect.jdbc.sink.dialect.DbDialect.CATALOG;
import static java.lang.String.format;
import static java.util.Collections.emptyMap;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static one.util.streamex.StreamEx.empty;
import static one.util.streamex.StreamEx.iterate;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCause;

public class SqlConnectorService
	extends ConnectorService<JdbcAuthConfig>
	implements MessageReader, DbListTreeServicePaged<JdbcSourceConnectorConfig> {
	public static final int CONNECTION_VALID_TIMEOUT_SEC = 5;
	public static final int SAMPLE_ROWS = 10;
	private static final Logger STATIC_LOGGER = LoggerFactory.getLogger(SqlConnectorService.class);

	private static final String TABLE_CATALOG = "TABLE_CATALOG";
	private Logger logger = STATIC_LOGGER;

	static {
		loadDrivers();
	}

	private static void loadDrivers() {
		driversForName();
		DriverManager.setLoginTimeout(600);

		// Sometimes sybase driver is not being automatically registered by Class.forName, in this case we have to manually register it
		Optional<Driver> sybDriver = DriverManager
				.drivers()
				.filter(it -> it.getClass().getName().contains("SybDriver"))
				.findFirst();

		if (sybDriver.isEmpty()) {
			try {
				DriverManager.registerDriver(new com.sybase.jdbc4.jdbc.SybDriver());
			} catch (Exception e) {
				STATIC_LOGGER.warn("error while loading driver sybase");
			}
		}
	}

	@SneakyThrows
	private static void driversForName() {
    loadDriver("com.simba.spark.jdbc.Driver");
		loadDriver("com.databricks.client.jdbc.Driver");
		loadDriver("com.amazon.athena.jdbc.AthenaDriver");
		loadDriver("com.amazon.redshift.jdbc.Driver");
		loadDriver("com.amazon.redshift.jdbc42.Driver");
		loadDriver("com.mysql.cj.jdbc.Driver");
		loadDriver("org.postgresql.Driver");
		loadDriver("oracle.jdbc.OracleDriver");
		loadDriver("com.microsoft.sqlserver.jdbc.SQLServerDriver");
		loadDriver("org.apache.hive.jdbc.HiveDriver");
		loadDriver("com.facebook.presto.jdbc.PrestoDriver");
		loadDriver("com.ibm.as400.access.AS400JDBCDriver");
		loadDriver("com.firebolt.FireboltDriver");
		loadDriver("com.teradata.jdbc.TeraDriver");
		loadDriver("com.netsuite.jdbc.openaccess.OpenAccessDriver");
		loadDriver("com.ibm.db2.jcc.DB2Driver");
		loadDriver("com.sap.db.jdbc.Driver");
		loadDriver("com.google.cloud.spanner.jdbc.JdbcDriver");
		loadDriver("com.snowflake.client.jdbc.SnowflakeDriver");
		loadDriver("com.sybase.jdbc4.jdbc.SybDriver");
	}

	private static void loadDriver(String className) {
		try {
			Class.forName(className);
		} catch (Exception e ) {
			STATIC_LOGGER.warn("error while loading driver " + className);
		}
	}

	private final AdminApiClient adminApiClient;
	private final CredentialsStore nexlaCredentialsStore;
	private ConnectionStrategy connectionStrategy;
	private ConnectionStrategy getConnectionStrategy(JdbcAuthConfig jdbcAuthConfig) {
		if (this.connectionStrategy == null) {
			this.connectionStrategy = ConnectionStrategy.getInstance(jdbcAuthConfig);
		}
		return this.connectionStrategy;
	}

	public SqlConnectorService(CredentialsStore nexlaCredentialsStore, AdminApiClient adminApiClient) {
		logger.info("creating new sql connector service, new SingleConnection is created");
		this.nexlaCredentialsStore = nexlaCredentialsStore;
		this.adminApiClient = adminApiClient;
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	public ListDataTypesResult listDataTypes(ConnectionType connectionType) {
		DbDialect dbDialect = DialectRegistry.getInstance().fromConnectionType(connectionType);

		List<String> dbTypes = dbDialect.getSqlTypes().toList();
		Map<String, String> nexlaTypeToDb = StreamEx
			.of(SchemaType.values())
			.filter(e -> e != SchemaType.NULL)
			.toMap(Object::toString, v -> getDbTypeForNexla(dbDialect, v));

		return new ListDataTypesResult(dbTypes, nexlaTypeToDb);
	}

	private String getDbTypeForNexla(DbDialect dbDialect, SchemaType v) {
		Schema.Type kafkaSchemaType = dbDialect.schemaTypeToKafkaType(v);
		return dbDialect.getSqlType(null, emptyMap(), kafkaSchemaType);
	}

	@Override
	public AuthResponse authenticate(JdbcAuthConfig authConfig) {
		try (Connection connection = getConnection(authConfig)) {
			boolean isNotSupportingValidation = authConfig.dbType.equals(NETSUITE_JDBC);
			return isNotSupportingValidation || connection.isValid(CONNECTION_VALID_TIMEOUT_SEC)
				? SUCCESS
				: authError(new IllegalStateException("Connection is not valid"));
		} catch (Exception e) {
			logger.error("Error while trying to get connection", e);
			return authError(e);
		}
	}


	public Connection getConnection(JdbcAuthConfig config) {
		return getConnectionRetry(config, 1, config.retryNum, false);
	}

	@SneakyThrows
	public Connection getConnectionRetry(JdbcAuthConfig config,
	                                     int tryNum,
	                                     int tryMax,
	                                     boolean sshTried) {
		try {
			// Log connection details for Databricks to verify user agent and other parameters
			if (config.dbType.toString().equals("DATABRICKS")) {
				logger.info("SqlConnectorService attempting Databricks connection for credsId={}", config.getCredsId());
			}
			return getConnectionStrategy(config).getConnection(config);
		} catch (Exception e) {
			if (tryNum < tryMax) {
				if (!sshTried && config.sshTunnelConfig.isPresent()) {
					new SqlTunnelSupport(config).createTunnel(logger);
					return getConnectionRetry(config, tryNum, tryMax,true);
				} else {
					return getConnectionRetry(config, tryNum + 1, tryMax, sshTried);
				}
			} else {
				logger.error("failed to get connection for credsId={}, credentialsType={}", config.getCredsId(), config.getCredentialsType(),  e);
				throw new SqlConnectionException(e);
			}
		}
	}

	@SneakyThrows
	public void truncateDestination(JdbcSinkConnectorConfig config) {
		try (
			Connection connection = getConnection(config.authConfig);
			Statement statement = connection.createStatement()
		) {
			JdbcAuthConfig auth = config.authConfig;
			DbDialect db = getDbDialect(auth);
			String table = db.getQualifiedTableName(config.table, auth.schemaName, JdbcUtils.getDatabaseName(config));
			statement.execute("DELETE FROM " + table);
			statement.close();
			connection.commit();
		}
	}

	/**
	 * Selects min/max ranges of incrementing and timestamp columns
	 */
	@SneakyThrows
	public MinMaxHolder selectMinMax(JdbcSourceConnectorConfig config) {
		JdbcAuthConfig auth = config.authConfig;
		DbDialect db = getDbDialect(auth);

		String innerQuery = config.query.orElseGet(() ->
			"SELECT * FROM " + db.getQualifiedTableName(config.table.get(), auth.schemaName, JdbcUtils.getDatabaseName(config)));

		final String sql;
		if (config.isIncrementingMode()) {
			String incrementingColumn = db.q(config.incrementingColumnName);
			sql = format("SELECT MIN(%s), MAX(%s) FROM (%s) a", incrementingColumn, incrementingColumn, innerQuery);

		} else if (config.isTimestampMode()) {

			String timestampColumn = db.q(config.timestampColumnName);
			sql = format("SELECT MIN(%s), MAX(%s) from (%s) a", timestampColumn, timestampColumn, innerQuery);

		} else if (config.isTimestampAndIncrementingMode()) {

			String incCol = db.q(config.incrementingColumnName);
			String tsCol = db.q(config.timestampColumnName);

			sql = format("SELECT MIN(%s), MAX(%s), MIN(%s), MAX(%s) FROM (%s) a", incCol, incCol, tsCol, tsCol, innerQuery);
		} else {
			throw new IllegalArgumentException("Unsupported configuration");
		}

		try (
			Connection connection = getConnection(auth);
			ResultSet rs = connection.createStatement().executeQuery(sql)
		) {
			rs.next();

			MinMaxHolder minMaxHolder = null;

			try {
				if (config.isIncrementingMode()) {

					minMaxHolder = new MinMaxHolder(
							toLong(rs.getObject(1)),
							toLong(rs.getObject(2)));

				} else if (config.isTimestampMode()) {

					minMaxHolder = new MinMaxHolder(
							db.getTimestamp(rs, 1),
							db.getTimestampRoundUp(rs, 2));
				} else if (config.isTimestampAndIncrementingMode()) {

					minMaxHolder = new MinMaxHolder(
							toLong(rs.getObject(1)),
							toLong(rs.getObject(2)),
							db.getTimestamp(rs, 3),
							db.getTimestampRoundUp(rs, 4));
				}
			} finally {
				connection.rollback();
			}

			return minMaxHolder;
		}
	}

	@Override
	@SneakyThrows
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		JdbcSourceConnectorConfig jdbcConfig = (JdbcSourceConnectorConfig) config;
		try (
			Connection connection = getConnection(jdbcConfig.authConfig);
			ResultSet schemas = connection.getMetaData().getCatalogs()
		) {
			List<NexlaBucket> buckets = new ArrayList<>();
			try {
				while (schemas.next()) {
					String tableSchema = schemas.getString(TABLE_CAT);
					buckets.add(new NexlaBucket(tableSchema));
				}
			} finally {
				connection.rollback();
			}
			return StreamEx.of(buckets);
		}
	}

	@Override
	@SneakyThrows
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig c) {
		JdbcSourceConnectorConfig config = (JdbcSourceConnectorConfig) c;
		try (Connection connection = getConnection(config.authConfig)) {
			Set<Table> tables;
			try {
				DbDialect dbDialect = getDbDialect(config.authConfig);
				NexlaDbInfo dbInfo = extractDbInfo(config.authConfig, dbDialect, connection);
				tables = selectTables(dbInfo, connection.getMetaData());
			} finally {
				connection.rollback();
			}
			return StreamEx.of(tables).map(t -> new NexlaFile(t.name, null, t.schema, null, null, null, null));
		}
	}

	@SneakyThrows
	private Set<Table> selectTables(NexlaDbInfo dbInfo, DatabaseMetaData metaData) {
		Set<Table> tablesResult = Sets.newHashSet();
		for (String schema : dbInfo.schemas) {
			ResultSet tables = metaData.getTables(dbInfo.dbName, schema, null, null);
			readTables(dbInfo, tablesResult, tables);
		}
		return tablesResult;
	}

	@SneakyThrows
	private Set<Table> readTables(NexlaDbInfo dbInfo, Set<Table> tables, ResultSet rs) {
		while (rs.next()) {
			String tableType = rs.getString(TABLE_TYPE);
			if (TABLE.equalsIgnoreCase(tableType) || VIEW.equalsIgnoreCase(tableType)) {
				tables.add(table(rs, dbInfo.dbName));
			}
		}
		return tables;
	}

	private static DbDialect getDbDialect(JdbcAuthConfig config) {
		return DialectRegistry.getInstance().fromConnectionString(config);
	}

	@Override
	@SneakyThrows
	public StreamEx<NexlaMessage> readStream(AbstractConfig c) {
		JdbcSourceConnectorConfig config = (JdbcSourceConnectorConfig) c;
		return doReadStream(config, false, Integer.MAX_VALUE).getValue0();
	}

	public Pair<StreamEx<NexlaMessage>, Optional<NexlaSchema>> readStreamWithSchema(AbstractConfig c) {
		JdbcSourceConnectorConfig config = (JdbcSourceConnectorConfig) c;
		Pair<StreamEx<NexlaMessage>, TableQuerier> result = doReadStream(config, false, Integer.MAX_VALUE);
		return Pair.with(result.getValue0(), result.getValue1().getNexlaSchema());
	}

	@SneakyThrows
	private Pair<StreamEx<NexlaMessage>, TableQuerier> doReadStream(JdbcSourceConnectorConfig config, boolean sample, int maxSampleRows) {
		BulkTableQuerier.QueryMode mode = config.query.map(a -> BulkTableQuerier.QueryMode.QUERY).orElse(BulkTableQuerier.QueryMode.TABLE);
		DbDialect dbDialect = getDbDialect(config.authConfig);

		Connection connection = null;
		TableQuerier querier = null;
		StreamEx<NexlaMessage> resultStream;
		try {
			connection = getConnection(config.authConfig);
			if (config.isUpdateQuery && config.query.isPresent()) {
				UpdateTableQuerier updateQuery = new UpdateTableQuerier(dbDialect, config, sample, config.query.get(), config.queryProbeTimeout);
				updateQuery.maybeStartQuery(connection);
				resultStream = StreamEx.of(new NexlaMessage(lhm("result", updateQuery.getResult())));
				querier = updateQuery;
			} else {
				BulkTableQuerier bulkQuerier = new BulkTableQuerier(
					dbDialect, config, sample, maxSampleRows, mode, config.authConfig.schemaName, config.table, config.query, Optional.of(config.queryProbeTimeout));

				logger.debug("Checking for next block of results from={}. {}", bulkQuerier.toString(), bulkQuerier.getRangeInfo());
				bulkQuerier.maybeStartQuery(connection);
				resultStream = loadResults(bulkQuerier, config, sample).map(NexlaMessage::new);
				querier = bulkQuerier;
			}
		} catch (SqlConnectionException e) {
			closeConnection(connection, querier, false);
			logger.error("Failed to run query", e);
			// retry on connectivity problem
			throw new ProbeRetriableException("Failed to run query", e);
		} catch (Exception e) {
			closeConnection(connection, querier, false);
			logger.error("Failed to run query", e);
			throw e;
		}

		Connection connectionTmp = connection;
		TableQuerier finalQuerier = querier;

		StreamEx<NexlaMessage> result = resultStream.onClose(() -> closeConnection(connectionTmp, finalQuerier, config.commitOnRead));
		return Pair.with(result, querier);
	}

	private void closeConnection(Connection connectionTemp, TableQuerier querierTemp, boolean commit) {
		if (connectionTemp != null) {
			try {
				if (commit) {
					connectionTemp.commit();
				} else {
					connectionTemp.rollback();
				}
				connectionTemp.close();
			} catch (Exception e) {
				// nothing to do
			}
		}
		closeSilently(querierTemp, connectionTemp);
	}

	/**
	 * Read records from cursor
	 */
	@SneakyThrows
	static StreamEx<LinkedHashMap<String, Object>> loadResults(
		BulkTableQuerier querier,
		JdbcSourceConnectorConfig config,
		boolean sample
	) {

		if (sample || config.isIncrementingMode() || config.isNoneMode()) {

			return querier.next()
				? iterate(querier.extractNexlaRecord(), o -> querier.extractNexlaRecord()).takeWhileInclusive(o -> querier.next())
				: empty();

		} else if (config.isTimestampMode() || config.isTimestampAndIncrementingMode()) {

			String tsColumn = config.timestampColumnName;
			// read batchSizeApprox rows and then read rows while where next row
			// ts = previous row ts
			int batchSizeApprox = config.batchSizeApprox;

			AtomicLong nRecords = new AtomicLong(0);
			AtomicLong lastTs = new AtomicLong(-1);

			return querier.next()
				? iterate(querier.extractNexlaRecord(), o -> querier.extractNexlaRecord()).takeWhileInclusive(o -> querier.next())
				.takeWhile(curr -> {
					long n = nRecords.incrementAndGet();
					if (n <= batchSizeApprox) {
						lastTs.set(((java.util.Date) curr.get(tsColumn)).getTime());
						return true;
					}

					if (lastTs.get() == -1) {
						return false;
					} else {
						long currTs = ((java.util.Date) curr.get(tsColumn)).getTime();
						return lastTs.get() == currTs;
					}
				})
				: empty();
		} else {
			throw new IllegalArgumentException("Unsupported mode");
		}
	}

	@Override
	public boolean checkWriteAccess(AbstractConfig config) {

		Map<String, String> params = config.originalsStrings();
		params.put(PRIMARY_KEY, "id");
		params.put(INSERT_MODE, "upsert");

		JdbcSinkConnectorConfig checkConfig = new JdbcSinkConnectorConfig(params);
		checkConfig.authConfig.withSshTunnelPorts(((JdbcSinkConnectorConfig) config).authConfig.getSshTunnelPorts());

		int randomId = Math.abs(new Random().nextInt());
		String tableName = "input_" + randomId;

		params.put(SqlConfigAccessor.TABLE, tableName);
		DbDialect dbDialect = getDbDialect(checkConfig.authConfig);

		try (
			Connection connection = getConnection(checkConfig.authConfig);
			Statement statement = connection.createStatement()
		) {
			dbDialect.executeStatement(statement, "CREATE TABLE " + tableName + " (ID INT, DESCRIPTION VARCHAR(100))");
			dbDialect.executeStatement(statement, "INSERT INTO " + tableName + " (ID, DESCRIPTION) VALUES(1, '123')");
			dbDialect.executeStatement(statement, "DROP TABLE " + tableName);
			statement.close();
			connection.commit();
			return true;
		} catch (Throwable e) {
			logger.error("Failed to check write access", e);
			return false;
		}
	}

	@Override
	public void close() {
		if (connectionStrategy != null) {
			connectionStrategy.disconnect();
		}
	}

	@Override
	public SqlSampleResult readSample(AbstractConfig c, boolean raw) {
		Pair<StreamEx<NexlaMessage>, TableQuerier> result = readSample(c);

		StreamEx<ProbeSampleResultEntry<LinkedHashMap<String, Object>>> rs = result.getValue0()
			.map(NexlaMessage::getRawMessage)
			.map(ProbeSampleResultEntry::new);

		return new SqlSampleResult(rs.toList(), result.getValue1().getFieldNames());
	}

	public Pair<StreamEx<NexlaMessage>, TableQuerier> readSample(AbstractConfig c) {
		JdbcSourceConnectorConfig config = (JdbcSourceConnectorConfig) c;
		config.batchSizeApprox = SAMPLE_ROWS;

		if (config.isIncrementingMode()) {
			config.internalIncrementingColumnFrom = config.incrementingLoadFromExclusive.orElse(0L);
			config.internalIncrementingColumnTo = config.internalIncrementingColumnFrom + SAMPLE_ROWS - 1;
		}

		return doReadStream(config, true, SAMPLE_ROWS);
	}

	@SneakyThrows
	@Override
	public void createDestination(AbstractConfig c, Optional<Integer> sourceId) {
		JdbcSinkConnectorConfig config = (JdbcSinkConnectorConfig) c;
		boolean dataBricksSkip = Objects.equals(DATABRICKS, config.authConfig.dbType) && !MODE_MANUAL.equals(config.mappingConfig.get().getMode());

		if (!config.mappingConfig.isPresent() || dataBricksSkip) {
			return;
		}

		List<String> keyColumns = config.primaryKey;

		MappingConfig mappingConfig = config.mappingConfig.get();
		DbDialect dbDialect = getDbDialect(config.authConfig);

		if ((!config.isEltFlowType() && !config.cdcEnabled) && !MODE_MANUAL.equals(mappingConfig.getMode())) {
			throw new ProbeException("Illegal mapping mode: " + mappingConfig.getMode());
		}

		List<String> nonKeyColumns = mappingConfig
				.getMapping()
				.values()
				.stream()
				.flatMap(e -> e.keySet().stream().filter(k -> !keyColumns.contains(k)))
				.collect(toList());

		Supplier<AutomaticBinding> automaticBinding = getBinding(c);

		if (isNotEmpty(keyColumns) || isNotEmpty(nonKeyColumns)) {
			try (Connection connection = getConnection(config.authConfig)) {
				String tableName = dbDialect.getQualifiedTableName(config.table, config.authConfig.schemaName, JdbcUtils.getDatabaseName(config));
				String createSql = dbDialect.getCreateSql(tableName, keyColumns, nonKeyColumns, automaticBinding, mappingConfig, config);
				logger.info("Creating DB table by query: {}", createSql);
				dbDialect.executePreparedStatement(connection.prepareStatement(createSql));
				if (!dbDialect.isAutoCommit()) {
					connection.commit();
				}
			} catch (Throwable e) {
				logger.error("Failed to create table", e);
				var databricksMessage = "[Simba][SparkJDBCDriver]";
				var message = e.getMessage();

				if (message.startsWith(databricksMessage) && message.contains("at ")) {
					// removing stack trace from message to not display it to end user
					message = message.substring(0, message.indexOf("at ")).trim();
				}

				throw new ProbeException(message, e);
			}
		}
	}

	@Override
	@SneakyThrows
	public void alterTable(AbstractConfig c) {
		JdbcSinkConnectorConfig config = (JdbcSinkConnectorConfig) c;
		if (!config.mappingConfig.isPresent()
				|| (Objects.equals(DATABRICKS, config.authConfig.dbType) && !MODE_MANUAL.equals(config.mappingConfig.get().getMode()))) {
			return;
		}

		List<String> nonKeyColumns;

		MappingConfig mappingConfig = config.mappingConfig.get();
		DbDialect dbDialect = getDbDialect(config.authConfig);

		switch (mappingConfig.getMode()) {
			// UI is sending only Manual mode for now
			case MODE_MANUAL:
				nonKeyColumns = mappingConfig
						.getMapping()
						.values()
						.stream()
						.flatMap(e -> e.keySet().stream())
						.collect(toList());
				break;
			default:
				throw new ProbeException("Illegal mapping mode: " + mappingConfig.getMode());
		}
		Supplier<AutomaticBinding> automaticBinding = getBinding(c);

		if (isNotEmpty(nonKeyColumns)) {
			try (Connection connection = getConnection(config.authConfig)) {
				String tableName = dbDialect.getQualifiedTableName(config.table, config.authConfig.schemaName, JdbcUtils.getDatabaseName(config));
				String createSql = dbDialect.getAlterSql(tableName, nonKeyColumns, automaticBinding, mappingConfig);
				logger.info("Alter DB table by query: {}", createSql);
				dbDialect.executePreparedStatement(connection.prepareStatement(createSql));
				if (dbDialect.isAutoCommit()) {
					connection.commit();
				}
			} catch (Throwable e) {
				logger.error("Failed to alter table", e);
				throw new ProbeException(e);
			}
		}
	}

	@SneakyThrows
	public void alterTableAdd(AbstractConfig c,  Set<String> nonKeyColumns) {
		JdbcSinkConnectorConfig config = (JdbcSinkConnectorConfig) c;

		for (String col : nonKeyColumns) {
			MappingConfig mappingConfig = config.mappingConfig.get();
			DbDialect dbDialect = getDbDialect(config.authConfig);

			Supplier<AutomaticBinding> automaticBinding = getBinding(c);

			try (Connection connection = getConnection(config.authConfig)) {
				String tableName = dbDialect.getQualifiedTableName(config.table, config.authConfig.schemaName, JdbcUtils.getDatabaseName(config));
				String addSql = dbDialect.getAlterAddSql(tableName, col, automaticBinding, mappingConfig);
				logger.info("Alter table ADD column by query: {}", addSql);
				dbDialect.executePreparedStatement(connection.prepareStatement(addSql));
				connection.commit();
			} catch (Throwable e) {
				logger.error("Failed to alter table ADD column", e);
				throw new ProbeException(e);
			}
		}
	}

	@SneakyThrows
	public void alterTableModify(AbstractConfig c,  Set<String> nonKeyColumns) {
		JdbcSinkConnectorConfig config = (JdbcSinkConnectorConfig) c;

		for (String col : nonKeyColumns) {
			MappingConfig mappingConfig = config.mappingConfig.get();
			DbDialect dbDialect = getDbDialect(config.authConfig);

			Supplier<AutomaticBinding> automaticBinding = getBinding(c);

			try (Connection connection = getConnection(config.authConfig)) {
				String tableName = dbDialect.getQualifiedTableName(config.table, config.authConfig.schemaName, JdbcUtils.getDatabaseName(config));
				List<String> modifySqls = dbDialect.getAlterModifySqls(tableName, col, automaticBinding, mappingConfig);

				for(String modifySql : modifySqls) {
					logger.info("Alter table MODIFY column by query: {}", modifySql);
					dbDialect.executePreparedStatement(connection.prepareStatement(modifySql));
				}
				connection.commit();
			} catch (Throwable e) {
				logger.error("Failed to alter table MODIFY column", e);
				throw new ProbeException(e);
			}
		}
	}

	private Supplier<AutomaticBinding> getBinding(AbstractConfig c) {
		Map<String, Object> originalConfig = c.originals();
		AutomaticBindingFactory automaticBindFact = new AutomaticBindingFactory(adminApiClient);
		return Suppliers.memoize(() -> automaticBindFact.create(originalConfig));
	}

	@SneakyThrows
	public void alterTableDrop(AbstractConfig c,  Set<String> nonKeyColumns) {
		JdbcSinkConnectorConfig config = (JdbcSinkConnectorConfig) c;

		for (String col : nonKeyColumns) {
			DbDialect dbDialect = getDbDialect(config.authConfig);

			try (Connection connection = getConnection(config.authConfig)) {
				String tableName = dbDialect.getQualifiedTableName(config.table, config.authConfig.schemaName, JdbcUtils.getDatabaseName(config));
				String dropSql = dbDialect.getAlterDropSql(tableName, col);
				logger.info("Alter table DROP column by query: {}", dropSql);
				dbDialect.executePreparedStatement(connection.prepareStatement(dropSql));
				connection.commit();
			} catch (Throwable e) {
				logger.error("Failed to alter table DROP column", e);
				throw new ProbeException(e);
			}
		}
	}

	public static boolean shouldRethrow(Exception e) {
		if (e instanceof ProbeRetriableException || e instanceof SqlConnectionException) {
			String sqlState = parseSqlState(e).orElse("");

			return (!sqlState.isEmpty() && getRetriableSqlState().contains(sqlState.substring(0, 2)))
				|| getRetriableSqlState().contains(sqlState);
		} else {
			return false;
		}
	}

	private static Optional<String> parseSqlState(Exception e) {
		if (e instanceof SQLException) {
			return ofNullable(((SQLException) e).getSQLState());
		}

		Optional<Throwable> rootCause = ofNullable(getRootCause(e));
		if (rootCause.map(x -> x instanceof SQLException).orElse(false)) {
			return ofNullable(((SQLException) rootCause.get()).getSQLState());
		}

		Optional<Throwable> expCause = ofNullable(e.getCause());
		if (expCause.map(x -> x instanceof SQLException).orElse(false)) {
			return ofNullable(((SQLException) expCause.get()).getSQLState());
		}

		return Optional.empty();
	}

	@Override
	@SneakyThrows
	public StreamEx<String> listDatabases(JdbcSourceConnectorConfig config) {
		DbDialect dbDialect = getDbDialect(config.authConfig);
		try (Connection conn = getConnection(config.authConfig)) {
			if (config.authConfig.databaseName != null){
				return StreamEx.of(config.authConfig.databaseName);
			} else {
				return getDatabasesFromConnectionMetadata(conn, dbDialect);
			}
		}
	}

	@Override
	@SneakyThrows
	public StreamEx<String> listSchemas(String database, JdbcSourceConnectorConfig config) {
		DbDialect dbDialect = getDbDialect(config.authConfig);
		try (Connection conn = getConnection(config.authConfig)) {
			NexlaDbInfo dbInfo = extractDbInfo(config.authConfig, dbDialect, conn);
			return StreamEx.of(dbInfo.schemas);
		}
	}

	@Override
	@SneakyThrows
	public StreamEx<String> listTables(String database, Optional<String> schema, JdbcSourceConnectorConfig config) {
		DbDialect dbDialect = getDbDialect(config.authConfig);
		try (Connection conn = getConnection(config.authConfig)) {
			if (database == null) {
				database = config.authConfig.databaseName;
			}
			if (schema.isEmpty()) {
				schema = Optional.ofNullable(config.authConfig.schemaName);
			}
			final boolean autoCommit = conn.getAutoCommit();
			if (!dbDialect.tableCatSupported()) {
				conn.setAutoCommit(true);
				database = null;
			}
			Set<Table> tables = TableListingUtils.listTables(Optional.ofNullable(database), schema, conn.getMetaData());
			conn.setAutoCommit(autoCommit);
			return StreamEx.of(tables).map(x -> x.name);
		}
	}

	@Override
	@SneakyThrows
	public List<ColumnInfo> listColumnInfos(String database, Optional<String> schema, String table, JdbcSourceConnectorConfig config) {
		DbDialect dbDialect = getDbDialect(config.authConfig);
		try (Connection conn = getConnection(config.authConfig)) {
			Set<String> primaryKeys = Collections.emptySet();
			final boolean autoCommit = conn.getAutoCommit();
			// We are temporarily disabling primary key fetch for redshift and snowflake to circumvent timeout issue
			if (!dbDialect.tableCatSupported()) {
				conn.setAutoCommit(true);
				database = null;
				table = table.toUpperCase();
			}
			boolean skipPrimaryKeysDetection = Objects.equals(config.authConfig.dbType, REDSHIFT) || Objects.equals(config.authConfig.dbType, SNOWFLAKE);
			if (dbDialect.supportPrimaryKeys() && !skipPrimaryKeysDetection) {
				primaryKeys = TableListingUtils.selectPrimaryKeyCols(database, schema.orElse(null), table, conn.getMetaData());
			}
			List<Column> tableColumns = selectTableColumns(dbDialect, database, schema.orElse(null), table, conn.getMetaData(), primaryKeys);
			conn.setAutoCommit(autoCommit);
			return StreamEx.of(tableColumns).map(TableListingUtils::columnInfo).toList();
		}
	}

	@Override
	@SneakyThrows
	public DbPage<Iterable<String>> listTables(JdbcSourceConnectorConfig config,
											   String database,
											   String schema,
											   Integer pageSize,
											   String offset) {
		DbDialect dbDialect = getDbDialect(config.authConfig);
		try (Connection conn = getConnection(config.authConfig)) {
			if (database == null) {
				database = config.authConfig.databaseName;
			}

			final boolean autoCommit = conn.getAutoCommit();
			if (!dbDialect.tableCatSupported()) {
				conn.setAutoCommit(true);
				database = null;
			}

			DbPage<Iterable<String>> tables = TableListingUtils.listTables(conn, database, pageSize, getOffset(offset));
			conn.setAutoCommit(autoCommit);
			return tables;
		}
	}

	private int getOffset(String offset) {
		try {
			return Integer.parseInt(offset);
		} catch (NumberFormatException ex) {
			return  0;
		}
	}

	@SneakyThrows
	private StreamEx<String> getDatabasesFromConnectionMetadata(Connection connection, DbDialect dbDialect) {
		Set<String> databases = Sets.newHashSet();

		if (dbDialect.getDatabaseTerm().equals(CATALOG)) {
			try (ResultSet dbCatalogs = connection.getMetaData().getCatalogs()) {
				while (dbCatalogs.next()) {
					String db = dbCatalogs.getString(TABLE_CAT);
					if (db != null) {
						databases.add(db);
					}
				}
			}
		} else {
			try(ResultSet dbSchemas = connection.getMetaData().getSchemas()) {
				while (dbSchemas.next()) {
					String db = dbSchemas.getString(TABLE_SCHEM);
					if (db != null) {
						databases.add(db);
					}
				}
			}
		}

		return StreamEx.of(databases);
	}

	@AllArgsConstructor
	private class SqlTunnelSupport implements SshTunnelSupport {

		JdbcAuthConfig authConfig;

		@Override
		public CredentialsStore credentialsStore() {
			return nexlaCredentialsStore;
		}

		@Override
		public Optional<BaseAuthConfig> authConfig() {
			return of(authConfig);
		}

		public List<HostPort> getHostPorts() {
			if (authConfig.url != null && !authConfig.url.isEmpty()
					&& (authConfig.host == null || authConfig.port == null)) {
				Optional<HostPort> hostPort = JdbcAuthConfig.extractHostPortFromUrl(authConfig.getCredsId(), authConfig.url);
				if (hostPort.isPresent()) {
					return Lists.newArrayList(hostPort.get());
				}
			}
			return Lists.newArrayList(new HostPort(authConfig.host, authConfig.port));
		}

	}

}
