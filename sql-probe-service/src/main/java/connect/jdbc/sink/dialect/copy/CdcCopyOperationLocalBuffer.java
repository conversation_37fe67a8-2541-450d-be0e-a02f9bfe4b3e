package connect.jdbc.sink.dialect.copy;

import com.nexla.connector.config.SinkConnectorConfig;

public class CdcCopyOperationLocalBuffer extends CopyOperationLocalBuffer {

	private final SinkCopyOperation localCopyOperation;

	public CdcCopyOperationLocalBuffer(SinkCopyOperation localCopyOperation, SinkConnectorConfig cfg) {
		super(cfg);
		this.localCopyOperation = localCopyOperation;
	}

	public SinkCopyOperation getLocalCopyOperation() {
		return localCopyOperation;
	}
}
