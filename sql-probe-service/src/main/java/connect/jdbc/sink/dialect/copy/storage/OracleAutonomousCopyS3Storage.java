package connect.jdbc.sink.dialect.copy.storage;

import com.nexla.connector.config.file.AWSAuthConfig;

import java.nio.file.Paths;

public class OracleAutonomousCopyS3Storage extends WarehouseCopyS3Storage {

	public OracleAutonomousCopyS3Storage(
			AWSAuthConfig config,
			String tempUploadBucket,
			String tempUploadPrefix,
			boolean deleteTempBucket
	) {
		super(config, tempUploadBucket, tempUploadPrefix, deleteTempBucket);
	}

	@Override
	public String getUrlPrefix() {
		return "https://";
	}

	@Override
	public String tempFileLocation(String fileName, String tempUploadPrefix) {
		return getUrlPrefix() + Paths.get(getTempUploadBucket() + ".s3.amazonaws.com", tempUploadPrefix, fileName);
	}
}
