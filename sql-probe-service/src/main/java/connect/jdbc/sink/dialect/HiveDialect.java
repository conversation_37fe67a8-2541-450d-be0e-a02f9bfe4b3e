package connect.jdbc.sink.dialect;

import connect.data.Schema;

import java.sql.ResultSet;
import java.util.Map;

import static java.util.Optional.empty;

public class HiveDialect extends DbDialect {

	public HiveDialect() {
		super("`", "`", empty(), empty(), empty(), false);
	}


	@Override
	public String getDbTypeByKafka(String schemaName, Map<String, String> parameters) {
		return null;
	}

	@Override
	public String getDbType(Schema.Type schemaName) {
		return null;
	}

	@Override
	public ResultSet fixResultSet(ResultSet resultSet) {
		return new HiveResultSet(resultSet);
	}
}
