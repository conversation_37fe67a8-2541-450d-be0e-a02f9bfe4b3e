package connect.jdbc.sink.dialect.copy;


import com.nexla.admin.client.DataCredentials;
import com.nexla.common.NexlaDataCredentials;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.probe.s3.S3ConnectorService;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DatabricksDialect;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.io.File;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.stream.Collectors.joining;

public class SinkDatabricksS3CopyOperation extends SinkS3CopyOperation {

	private static final DatabricksDialect DIALECT = new DatabricksDialect();

	@Override
	@SneakyThrows
	protected void executeCopyCommand(String blobLocation, Connection connection, Optional<ReplicationContext> replicationContext) {
		logger.info("SinkDatabricksS3CopyOperation executing copy command with connection: {}", connection.toString());
		logger.info("SinkDatabricksS3CopyOperation blob location: {}", blobLocation);
		
		final boolean tableExist = isTableExist(connection, config.table);
		logger.info("delta table {} already exists: {}", config.table, tableExist);
		if (tableExist) {
			executeStatement(connection, copyCommand(schema, blobLocation, config.table, replicationContext));
		} else {
			executeStatement(connection, createDeltaTableCommand(blobLocation));
		}
	}

	@Override
	@SneakyThrows
	protected void executeUpsert(Statement st, File localFile, String tempTable, Optional<ReplicationContext> replicationContext) {
		Connection conn = st.getConnection();
		final boolean tableExist = isTableExist(conn, config.table);
		logger.info("delta table {} already exists: {}", config.table, tableExist);
		if (tableExist) {
			logUpdated(st.executeUpdate(logSql(mergeCommand(config.table, config.primaryKey, tempFileLocation(localFile.getName())))));
		} else {
			executeStatement(conn, createDeltaTableCommand(tempFileLocation(localFile.getName())));
		}
	}

	public static boolean isTableExist(Connection connection, String table) throws SQLException {
		boolean tableExist = false;
		Statement checkStatement = connection.createStatement();
		final ResultSet tablesRS = checkStatement.executeQuery("SHOW TABLES");
		while (tablesRS.next()) {
			if (table.equals(tablesRS.getString("tableName"))) {
				tableExist = true;
				break;
			}
		}
		return tableExist;
	}

	@Override
	public DbDialect dbDialect() {
		return DIALECT;
	}

	@Override
	@SneakyThrows
	protected void runUpsert(Optional<List<String>> columnsOpt, File localFile, Connection conn, Optional<ReplicationContext> replicationContext) {
		Statement st = conn.createStatement();
		executeUpsert(st, localFile, StringUtils.EMPTY, replicationContext);
	}

	private String mergeCommand(String qualifiedTableName, List<String> primaryKey, String tempFileLocation) {
		final String condition = primaryKey.stream()
			.map(k -> String.format("target.%s = source.%s", k, k))
			.collect(joining(" AND "));
		return String.format("MERGE INTO %s target\n" +
				" USING json.`%s` source\n" +
				" ON %s\n" +
				" WHEN MATCHED THEN\n" +
				"  UPDATE SET *\n" +
				" WHEN NOT MATCHED\n" +
				"  THEN INSERT *"
			, qualifiedTableName, tempFileLocation, condition);
	}

	@Override
	protected void initCloudObjectStoreClient() {
		initS3Client();
	}

	private void initS3Client() {
		final Integer databricksCloudCredentialsId = config.authConfig.databricksCloudCredentialsId;
		final AWSAuthConfig.BucketPrefix databricksTempS3Bucket = config.authConfig.databricksTempS3Bucket;
		if (databricksCloudCredentialsId == null && databricksTempS3Bucket == null) {
			super.initCloudObjectStoreClient();
		} else {
			DataCredentials creds = adminApiClient.getDataCredentials(databricksCloudCredentialsId).get();
			Map<String, String> credsMap = NexlaDataCredentials.getCreds(config.decryptKey, creds.getCredentialsEnc(), creds.getCredentialsEncIv());
			AWSAuthConfig awsAuthConfig = new AWSAuthConfig(credsMap, databricksCloudCredentialsId);
			this.s3Client = S3ConnectorService.createS3ClientFromCreds(awsAuthConfig, null);
		}
		this.lastRefreshTime = DateTime.now();
	}

	private S3Client getS3Client() {
		if (DateTime.now().isAfter(lastRefreshTime.getMillis() + MAX_TOKEN_DURATION)) {
			initS3Client();
		}
		return s3Client;
	}

	@Override
	protected void deleteCloudFile(File localFile) {
		if (config.tempS3Delete) {
			String s3FileName = config.tempS3UploadPrefix + "/" + localFile.getName();
			withClientRetrievable(() -> s3Client, (s3Client) -> {
				DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
						.bucket(config.tempS3UploadBucket)
						.key(s3FileName)
						.build();
				s3Client.deleteObject(deleteObjectRequest);
			});
		}
	}

	@Override
	public void uploadFile(File localFile) {
		logger.info(
				"putObject(" + config.tempS3UploadBucket + ", " +
						config.tempS3UploadPrefix + "/" + localFile.getName() + ")");

		S3Client s3Client = getS3Client();
		PutObjectRequest putObjectRequest = PutObjectRequest.builder()
				.bucket(config.tempS3UploadBucket)
				.key(config.tempS3UploadPrefix + "/" + localFile.getName())
				.build();
		s3Client.putObject(putObjectRequest, RequestBody.fromFile(localFile));
	}

	@Override
	String tempFileLocation(String fileName) {
		return "s3a://" + Paths.get(getTempBucket(config), getTempBucketPrefix(config), fileName);
	}

	@Override
	public String copyCommand(Schema schema, String s3FileLocation, String table, Optional<ReplicationContext> replicationContext) {
		return String.format("COPY INTO %s\n"
				+ "  FROM '%s'\n"
				+ "  FILEFORMAT = JSON\n",
			getQualifiedTableName(), s3FileLocation);
	}

	private String createDeltaTableCommand(String s3FileLocation) {
		String location = Optional.ofNullable(config.authConfig.databricksDestinationS3Bucket)
			.map(destination -> {
				String tableS3Path = Paths.get(destination.bucket, destination.prefix, config.table).toString();
				return String.format("LOCATION 's3a://%s'", tableS3Path);
			})
			.orElse("");
		return String.format("CREATE TABLE %s\n"
				+ "  USING delta\n"
				+ "  %s"
				+ "  AS SELECT *\n"
				+ "  FROM json.`%s`\n",
			getQualifiedTableName(), location, s3FileLocation);
	}

	@Override
	protected String createCommand(String tempTable, String qualifiedTableName) {
		// no temp table, we use databricks merge for upsert
		return "";
	}

	@Override
	public void dropTempTable(Statement stmt, String tempTable) {
		// no temp table, we use databricks merge for upsert
	}

	private String getQualifiedTableName() {
		return Optional.ofNullable(config.authConfig.schemaName)
			.map(schema -> String.format("%s.%s", DIALECT.q(schema), DIALECT.q(config.table)))
			.orElse(DIALECT.q(config.table));
	}

	private String getTempBucket(JdbcSinkConnectorConfig config) {
		return Optional.ofNullable(config.authConfig.databricksTempS3Bucket)
			.map(b -> b.bucket)
			.orElse(config.tempS3UploadBucket);
	}

	private String getTempBucketPrefix(JdbcSinkConnectorConfig config) {
		return Optional.ofNullable(config.authConfig.databricksTempS3Bucket)
			.map(b -> b.prefix)
			.orElse(config.tempS3UploadPrefix);
	}

	@SneakyThrows
	private void executeStatement(Connection connection, String command) {
		try (PreparedStatement statement = connection.prepareStatement(logSql(command))) {
			statement.execute();
		}
	}

}
