package connect.jdbc.sink.dialect

import com.google.common.base.Suppliers
import com.google.common.collect.Maps
import com.nexla.common.{ConnectionType, NexlaConstants}
import com.nexla.connector.ConnectorService
import com.nexla.connector.config.MappingConfig
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig
import com.nexla.connector.properties.SqlConfigAccessor
import com.nexla.common.schema.SchemaType
import org.scalatest.TagAnnotation
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers

import java.util

@TagAnnotation("com.nexla.test.ScalaUnitTests")
class FireboltSqlDialectTest extends AnyFlatSpecLike with Matchers {

  val TYPE_BINDING = util.Map.of("id", SchemaType.INTEGER,
    "data_set_id", SchemaType.BOOLEAN,
    "name", SchemaType.STRING,
    "description", SchemaType.STRING)

  val FORMAT_BINDING = util.Map.of("description", "date")

  "getCreateSql" should "create correct SQL" in {
    val config = configs("FACT")
    val dbDialect: DbDialect = new FireboltSqlDialect
    val qualifiedName: String = dbDialect.getQualifiedTableName("unit_test_table", "", "")
    val createSql: String = dbDialect.getCreateSql(qualifiedName, util.List.of("id", "data_set_id"),
      util.List.of("name", "type", "description"),
      Suppliers.memoize( () => new AutomaticBinding(TYPE_BINDING, FORMAT_BINDING)),
      new MappingConfig, new JdbcSinkConnectorConfig(config))
    val expectedQuery: String = """CREATE FACT TABLE IF NOT EXISTS "unit_test_table" ( "id" INT NULL,"data_set_id" BOOLEAN NULL,"name" TEXT NULL,"type" TEXT NULL,"description" DATE NULL ) PRIMARY INDEX "id","data_set_id""""
    createSql shouldEqual expectedQuery
  }

  private def configs(tableType: String): util.Map[String, String] = {
    val properties: util.Map[String, String] = Maps.newHashMap[String, String]
    properties.put(NexlaConstants.CREDENTIALS_TYPE, ConnectionType.FIREBOLT.name())
    properties.put(NexlaConstants.SINK_ID, "1")
    properties.put(ConnectorService.UNIT_TEST, "true")
    properties.put(SqlConfigAccessor.INSERT_MODE, "insert")
    properties.put(JdbcSinkConnectorConfig.FIREBOLT_TABLE_TYPE, tableType)
    properties
  }

}
