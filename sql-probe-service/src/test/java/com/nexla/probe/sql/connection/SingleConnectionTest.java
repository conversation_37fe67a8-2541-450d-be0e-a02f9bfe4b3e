package com.nexla.probe.sql.connection;

import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import com.nexla.test.UnitTests;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.experimental.categories.Category;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.*;

import static com.nexla.common.ConnectionType.*;
import static com.nexla.common.StreamUtils.map;
import static org.junit.Assert.*;

@Category(UnitTests.class)
public class SingleConnectionTest {

	@Test
	public void urlWithRedshiftTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(REDSHIFT);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("**********************************************", url);
	}

	@Test
	public void urlWithRedshiftNoSchemaTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(REDSHIFT);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("**********************************************", url);
	}

	@Test
	public void urlWithSnowflakeTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(SNOWFLAKE);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("************************************************************", url);
	}

	@Test
	public void urlWithSnowflakeNoSchemaTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(SNOWFLAKE);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("************************************************************", url);
	}

	@Test
	public void urlWithSnowflakeWarehouseTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(SNOWFLAKE);
		config.put("warehouse_name", "testWarehouse");
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("************************************************************&warehouse=testWarehouse", url);
	}

	@Test
	public void urlWithSnowflakeRSAAuthTest() throws IOException {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(SNOWFLAKE);
		String privateKey = new String(this.getClass().getResourceAsStream("/base_64_rsa_key_no_enc.p8").readAllBytes(), StandardCharsets.UTF_8);
		config.put("snowflake.privateKey", privateKey);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		Properties properties = singleConnection.getProperties(jdbcAuthConfig);
		assertNull(properties.getProperty("password"));
		assertFalse(properties.get("privateKey") == null);
	}

	@Test
	public void urlWithMysqlTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(MYSQL);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("*****************************************************************************************", url);
	}

	@Test
	public void urlWithMysqlLegacyTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(MYSQL);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig, ConnectionTlsStrategy.DefaultTls);
		assertEquals("*************************************************************", url);
	}

	@Test
	public void urlWithPostgresTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(POSTGRES);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("*********************************", url);
	}

	@Test
	public void urlWithDatabricksTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(DATABRICKS);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("*******************************************************************************************************************************************", url);
	}

	@Test
	public void urlWithPostgresNoSchemaTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(POSTGRES);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("*********************************", url);
	}

	@Test
	public void urlWithSpannerTest() {
		SingleConnection singleConnection = new SingleConnection();
		Map<String, String> config = createConfig(GCP_SPANNER);
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(config, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		String urlWithConfigPathTrimmed = url.substring(0, url.indexOf("credentials="));
		assertEquals("jdbc:cloudspanner:/projects/test_project/instances/test_instance/databases/test_db?", urlWithConfigPathTrimmed);
	}

	@Test
	public void urlWithFirebolt() {
		Map<String, Object> props = new HashMap<>();
		props.put("host", "api.app.firebolt.io");
		props.put("credentials_type", "firebolt");
		props.put("database_name", "unit_test");
		props.put("username", "<EMAIL>");
		SingleConnection singleConnection = new SingleConnection();
		JdbcAuthConfig jdbcAuthConfig = new JdbcAuthConfig(props, 123);
		String url = singleConnection.getUrl(jdbcAuthConfig);
		assertEquals("*********************************************", url);
	}

	@Test
	@SneakyThrows
	public void testOracleAdwDefaultProps() {
		Map<String, Object> props = new HashMap<>();
		String wallet = new String(this.getClass().getResourceAsStream("/walletstub.zip.b64").readAllBytes(), StandardCharsets.UTF_8);
		props.put("oracle_wallet", wallet);
		props.put("credentials_type", "oracle_autonomous");
		List<String> calledUrls = new ArrayList<>();

		NexlaJDBCConnectionFactory mockFactory = new NexlaJDBCConnectionFactory() {
			@Override
			public Connection createConnection(String url, Properties properties) {
				calledUrls.add(url);
				throw new RuntimeException(new java.sql.SQLSyntaxErrorException(": ORA-04088: error during execution of trigger 'OAX$DW.SESS_LOGON_SERVICE_HIGH'\n\nORA-20001: You are not allowed to connect to the HIGH service."));
			}

			@Override
			public void close() {

			}
		};
		SingleConnection wrap = new SingleConnection(mockFactory);
		JdbcAuthConfig adwAuthConfig = new JdbcAuthConfig(props, 123);

		// exception must be thrown
		RuntimeException exception = assertThrows(RuntimeException.class, () -> wrap.getConnection(adwAuthConfig));

		// original cause must be preserved
		String expectedMessage = "ORA-20001: You are not allowed to connect to the HIGH service.";
		String actualMessage = exception.getMessage();
		assertTrue(actualMessage.contains(expectedMessage));

		// retries must be done from highest tier Oracle ADW service to lowest
		assertTrue(calledUrls.get(0).contains("**********************************************="));
		assertTrue(calledUrls.get(1).contains("************************************************="));
		assertTrue(calledUrls.get(2).contains("*********************************************="));
		// no more than 3 retries
		assertEquals(3, calledUrls.size());
	}

	@Test
	@SneakyThrows
	public void testOracleAdwWithMedium() {
		Map<String, Object> props = new HashMap<>();
		String wallet = new String(this.getClass().getResourceAsStream("/walletstub.zip.b64").readAllBytes(), StandardCharsets.UTF_8);
		props.put("oracle_wallet", wallet);
		props.put("oracle_service_name", "_medium");
		props.put("credentials_type", "oracle_autonomous");
		List<String> calledUrls = new ArrayList<>();

		NexlaJDBCConnectionFactory mockFactory = new NexlaJDBCConnectionFactory() {
			@Override
			public Connection createConnection(String url, Properties properties) {
				calledUrls.add(url);
				throw new RuntimeException(new java.sql.SQLSyntaxErrorException(": ORA-04088: error during execution of trigger 'OAX$DW.SESS_LOGON_SERVICE_HIGH'\n\nORA-20001: You are not allowed to connect to the service."));
			}

			@Override
			public void close() {

			}
		};
		SingleConnection wrap = new SingleConnection(mockFactory);
		JdbcAuthConfig adwAuthConfig = new JdbcAuthConfig(props, 123);

		// exception must be thrown
		assertThrows(RuntimeException.class, () -> wrap.getConnection(adwAuthConfig));

		// retries must be done from highest available tier Oracle ADW service to lowest
		assertTrue(calledUrls.get(0).contains("************************************************="));
		assertTrue(calledUrls.get(1).contains("*********************************************="));
		// no more than 2 retries since we already were on medium
		assertEquals(2, calledUrls.size());
	}

	@Test
	public void testGetPropertiesWhenNetsuiteTBAEnabled() {
		var strategy = new SingleConnection();
		Map<String, ?> props = map(
				NexlaConstants.CREDENTIALS_TYPE, NETSUITE_JDBC.name().toLowerCase(),
				"tba_enabled", "true",
				"url", "jdbc:ns://account.connect.api.netsuite.com:1708;ServerDataSource=NetSuite2.com;Encrypted=1;NegotiateSSLClose=false;CustomProperties=(AccountID=account;RoleID=role)",
				"account_id", "account_id",
				"consumer_key", "consumer_key",
				"consumer_secret", "consumer_secret",
				"token", "token",
				"token_secret", "token_secret",
				"schema_name", "schema_name",
				"database_name", "database_name",
				"server_zone_id", "UTC"
		);
		var authConfig = new JdbcAuthConfig(props, 0);

		var properties = strategy.getProperties(authConfig);

		assertTrue(authConfig.netsuiteTBAAuthConfig.isPresent());
		assertEquals("TBA", properties.get("user"));

		var passwordToken = properties.get("password").toString().split("&");
		assertEquals(7, passwordToken.length);
		assertEquals(props.get("account_id"), passwordToken[0]);
		assertEquals(props.get("consumer_key"), passwordToken[1]);
		assertEquals(props.get("token"), passwordToken[2]);
		assertEquals(UUID.fromString(passwordToken[3]).toString(), passwordToken[3]);
		assertTrue(StringUtils.isNumeric(passwordToken[4]));
		assertTrue(passwordToken[5].endsWith("="));
		assertEquals("HMAC-SHA256", passwordToken[6]);
	}

	@Test
	public void testGetPropertiesWhenNetsuiteTBANotEnabled() {
		var strategy = new SingleConnection();
		Map<String, ?> props = map(
				NexlaConstants.CREDENTIALS_TYPE, NETSUITE_JDBC.name().toLowerCase(),
				"tba_enabled", "false",
				"url", "jdbc:ns://account.connect.api.netsuite.com:1708;ServerDataSource=NetSuite2.com;Encrypted=1;NegotiateSSLClose=false;CustomProperties=(AccountID=account;RoleID=role)",
				"account_id", "account_id",
				"consumer_key", "consumer_key",
				"consumer_secret", "consumer_secret",
				"token", "token",
				"token_secret", "token_secret",
				"schema_name", "schema_name",
				"database_name", "database_name",
				"server_zone_id", "UTC",
				"username", "username",
				"password", "password"
		);
		var authConfig = new JdbcAuthConfig(props, 0);

		var properties = strategy.getProperties(authConfig);

		assertFalse(authConfig.netsuiteTBAAuthConfig.isPresent());
		assertEquals(props.get("username"), properties.get("user"));
		assertEquals(props.get("password"), properties.get("password"));
	}

	private Map<String, String> createConfig(ConnectionType connectionType) {
		Map<String, String> map = map("username", "qa",
				"password", "pwd",
				"host", "nexla",
				"port", "3234");
		if (connectionType.equals(REDSHIFT)) {
			map.put("database_name", "redshiftdb");
			map.put("credentials_type", "redshift");
			map.put("schema_name", "public");
		} else if (connectionType.equals(SNOWFLAKE)) {
			map.put("database_name", "snowflakedb");
			map.put("credentials_type", "snowflake");
			map.put("schema_name", "public");
		} else if (connectionType.equals(MYSQL)) {
			map.put("database_name", "mysqldb");
			map.put("credentials_type", "mysql");
		} else if (connectionType.equals(POSTGRES)) {
			map.put("database_name", "pgdb");
			map.put("credentials_type", "postgres");
			map.put("schema_name", "public");
		} else if (connectionType.equals(GCP_SPANNER)) {
			map.put("instance_id", "test_instance");
			map.put("project_id", "test_project");
			map.put("database_name", "test_db");
			map.put("json_creds", "{\"key\": \"value\"}");
			map.put("credentials_type", "gcp_spanner");
		} else if (connectionType.equals(DATABRICKS)) {
			map.put("database_name", "databricksdb");
			map.put("credentials_type", "databricks");
			map.put("schema_name", "public");
			map.put("warehouse_name", "warehouse123");
		}
		return map;
	}
}
