package com.nexla.redis;

import static com.nexla.redis.dto.MasterEntry.DataModelVersion.V1;
import static com.nexla.redis.dto.MasterEntry.DataModelVersion.V2;
import static com.nexla.redis.dto.MasterEntry.MIGRATION_STARTED_AT;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.nexla.admin.client.DataMap;
import com.nexla.redis.dto.MasterEntry;
import com.nexla.redis.exception.LookupMigrationException;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class RedisLookupMigrationTest extends RedisDataMapBaseTest {

  @BeforeAll
  public static void beforeAll() {
    startUp();
  }

  private static DataMap dataMap(
      int id,
      MasterEntry.DataModelVersion dataModelVersion,
      List<Map<String, String>> data,
      boolean useVersioning) {
    DataMap dm = new DataMap();
    dm.setId(id);
    dm.setMapPrimaryKey("UID");
    dm.setUseVersioning(useVersioning);
    dm.setEmitDataDefault(true);
    dm.setDataMap(data);
    dm.setDataModelVersion(dataModelVersion.toString());
    return dm;
  }

  public static Stream<Arguments> migrateFailureTestCases() {
    return Stream.of(
        Arguments.of(1121, V1, V1, V1, "Invalid args: from/to version can not be the same"),
        Arguments.of(1122, V2, V2, V2, "Invalid args: from/to version can not be the same"),
        Arguments.of(1123, V2, V1, V1, "Invalid args: from/to version can not be the same"),
        Arguments.of(1124, V1, V2, V2, "Invalid args: from/to version can not be the same"),
        Arguments.of(1125, V1, V2, V1, "Invalid args: expected version=V2, actual version=V1"),
        Arguments.of(1126, V2, V1, V2, "Invalid args: expected version=V1, actual version=V2"));
  }

  @ParameterizedTest
  @MethodSource("migrateFailureTestCases")
  @SneakyThrows
  public void testMigrateDataModelVersionFailure(
      int id,
      MasterEntry.DataModelVersion initialVersion,
      MasterEntry.DataModelVersion from,
      MasterEntry.DataModelVersion to,
      String exceptionMessage) {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, id);
    try {
      lookup.save(
          dataMap(
              id,
              initialVersion,
              List.of(
                  Map.of("UID", "one", "val", "A"),
                  Map.of("UID", "two", "val", "B"),
                  Map.of("UID", "three")),
              false));

      var ex =
          assertThrows(
              LookupMigrationException.class, () -> lookup.migrateDataModelVersion(from, to));
      assertEquals(exceptionMessage, ex.getMessage());
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testMigrateDataModelVersionFromV1ToV2_WithoutVersioning() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1131);
    try {
      lookup.save(
          dataMap(
              lookup.getId(),
              V1,
              List.of(
                  Map.of("UID", "one", "val", "A"),
                  Map.of("UID", "two", "val", "B"),
                  Map.of("UID", "three")),
              false));
      assertEquals(V1, lookup.getMasterEntry().dataModelVersion);
      assertEquals(0, JEDIS.exists("dm:entries:" + lookup.getId()));

      lookup.migrateDataModelVersion(V1, V2);

      var masterEntry = lookup.getMasterEntry();
      assertEquals(V2, masterEntry.dataModelVersion);
      assertEquals(3, masterEntry.mapSize);
      assertFalse(masterEntry.useVersioning);
      assertEquals(1, JEDIS.exists("dm:entries:" + lookup.getId()));
      assertEquals(3, lookup.countEntries("1", lookup.getMasterEntry().dataModelVersion));
      assertEquals(Map.of("UID", "one", "val", "A"), lookup.getEntry("1", "one"));
      assertEquals(
          Set.of(
              Map.of("UID", "one", "val", "A"),
              Map.of("UID", "two", "val", "B"),
              Map.of("UID", "three")),
          Set.copyOf(lookup.getAllEntries()));
      assertEquals(
          List.of("dm:entry:1131:1:one", "dm:entry:1131:1:three", "dm:entry:1131:1:two"),
          lookup.getPrimaryKeys().stream().sorted().collect(Collectors.toList()));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testMigrateDataModelVersionFromV1ToV2_WithVersioning() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1231);
    try {
      lookup.save(
          dataMap(
              lookup.getId(),
              V1,
              List.of(
                  Map.of("UID", "one", "val", "A"),
                  Map.of("UID", "two", "val", "B"),
                  Map.of("UID", "three")),
              true));
      assertEquals(V1, lookup.getMasterEntry().dataModelVersion);
      assertEquals(0, JEDIS.exists("dm:entries:" + lookup.getId()));

      lookup.migrateDataModelVersion(V1, V2);

      var masterEntry = lookup.getMasterEntry();
      assertEquals(V2, masterEntry.dataModelVersion);
      assertEquals(3, masterEntry.mapSize);
      assertTrue(masterEntry.useVersioning);
      assertEquals(1, JEDIS.exists("dm:entries:" + lookup.getId()));
      assertEquals(3, lookup.countEntries("1", lookup.getMasterEntry().dataModelVersion));
      assertEquals(Map.of("UID", "one", "val", "A"), lookup.getEntry("1", "one"));
      assertEquals(
          Set.of(
              Map.of("UID", "one", "val", "A"),
              Map.of("UID", "two", "val", "B"),
              Map.of("UID", "three")),
          Set.copyOf(lookup.getAllEntries()));
      assertEquals(
          List.of("dm:entry:1231:1:one", "dm:entry:1231:1:three", "dm:entry:1231:1:two"),
          lookup.getPrimaryKeys().stream().sorted().collect(Collectors.toList()));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testMigrateDataModelVersionFromV1ToV2_WithVersioningUpdateInProgress() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1331);
    try {
      lookup.save(
          dataMap(
              lookup.getId(),
              V1,
              List.of(
                  Map.of("UID", "one", "val", "A"),
                  Map.of("UID", "two", "val", "B"),
                  Map.of("UID", "three")),
              true));

      // emulate dynamic lookup update
      lookup.beginUpdateEntries();
      lookup.updateEntries(
          List.of(Map.of("UID", "five", "val", "D"), Map.of("UID", "six", "val", "E")));

      assertEquals(V1, lookup.getMasterEntry().dataModelVersion);
      assertEquals(0, JEDIS.exists("dm:entries:" + lookup.getId()));
      assertEquals(
          Set.of(
              Map.of("UID", "one", "val", "A"),
              Map.of("UID", "two", "val", "B"),
              Map.of("UID", "three")),
          Set.copyOf(lookup.getAllEntries()));
      var masterEntry = lookup.getMasterEntry();
      assertEquals("1", masterEntry.currentVersion);
      assertEquals("2", masterEntry.nextVersion);
      assertEquals(2, masterEntry.mapSize);

      lookup.migrateDataModelVersion(V1, V2);

      masterEntry = lookup.getMasterEntry();
      assertEquals(V2, masterEntry.dataModelVersion);
      assertTrue(masterEntry.useVersioning);
      assertEquals(1, JEDIS.exists("dm:entries:" + lookup.getId()));
      assertEquals(1, JEDIS.exists("dm:next_version_entries:" + lookup.getId()));
      assertEquals(3, lookup.countEntries("1", lookup.getMasterEntry().dataModelVersion));
      assertEquals(Map.of("UID", "one", "val", "A"), lookup.getEntry("1", "one"));
      assertEquals(
          Set.of(
              Map.of("UID", "one", "val", "A"),
              Map.of("UID", "two", "val", "B"),
              Map.of("UID", "three")),
          Set.copyOf(lookup.getAllEntries()));
      assertEquals("1", masterEntry.currentVersion);
      assertEquals("2", masterEntry.nextVersion);
      assertEquals(2, masterEntry.mapSize);
      assertEquals(
          List.of("dm:entry:1331:1:one", "dm:entry:1331:1:three", "dm:entry:1331:1:two"),
          lookup.getPrimaryKeys().stream().sorted().collect(Collectors.toList()));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testMigrateDataModelVersionFromV2ToV1_WithoutVersioning() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1141);
    try {
      lookup.save(
          dataMap(
              lookup.getId(),
              V2,
              List.of(
                  Map.of("UID", "one", "val", "A"),
                  Map.of("UID", "two", "val", "B"),
                  Map.of("UID", "three")),
              false));
      assertEquals(lookup.getMasterEntry().dataModelVersion, V2);
      assertEquals(1, JEDIS.exists("dm:entries:" + lookup.getId()));

      lookup.migrateDataModelVersion(V2, V1);

      var masterEntry = lookup.getMasterEntry();
      assertEquals(V1, masterEntry.dataModelVersion);
      assertEquals(3, masterEntry.mapSize);
      assertFalse(masterEntry.useVersioning);
      assertEquals(0, JEDIS.exists("dm:entries:" + lookup.getId()));
      assertEquals(3, lookup.countEntries("1", lookup.getMasterEntry().dataModelVersion));
      assertEquals(Map.of("UID", "one", "val", "A"), lookup.getEntry("1", "one"));
      assertEquals(
          List.of("dm:entry:1141:1:one", "dm:entry:1141:1:three", "dm:entry:1141:1:two"),
          lookup.getPrimaryKeys().stream().sorted().collect(Collectors.toList()));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testMigrateDataModelVersionFromV2ToV1_WithVersioning() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1241);
    try {
      lookup.save(
          dataMap(
              lookup.getId(),
              V2,
              List.of(
                  Map.of("UID", "one", "val", "A"),
                  Map.of("UID", "two", "val", "B"),
                  Map.of("UID", "three")),
              true));
      // emulate dynamic lookup update
      lookup.beginUpdateEntries();
      lookup.updateEntries(
          List.of(Map.of("UID", "five", "val", "D"), Map.of("UID", "six", "val", "E")));

      assertEquals(lookup.getMasterEntry().dataModelVersion, V2);
      assertEquals(1, JEDIS.exists("dm:entries:" + lookup.getId()));
      assertEquals(
          Set.of(
              Map.of("UID", "one", "val", "A"),
              Map.of("UID", "two", "val", "B"),
              Map.of("UID", "three")),
          Set.copyOf(lookup.getAllEntries()));
      var masterEntry = lookup.getMasterEntry();
      assertEquals("1", masterEntry.currentVersion);
      assertEquals("2", masterEntry.nextVersion);
      assertEquals(2, masterEntry.mapSize);

      lookup.migrateDataModelVersion(V2, V1);

      masterEntry = lookup.getMasterEntry();
      assertEquals(V1, masterEntry.dataModelVersion);
      assertEquals("1", masterEntry.currentVersion);
      assertEquals("2", masterEntry.nextVersion);
      assertEquals(2, masterEntry.mapSize);
      assertTrue(masterEntry.useVersioning);
      assertEquals(0, JEDIS.exists("dm:entries:" + lookup.getId()));
      assertEquals(0, JEDIS.exists("dm:next_version_entries:" + lookup.getId()));
      assertEquals(3, lookup.countEntries("1", lookup.getMasterEntry().dataModelVersion));
      assertEquals(Map.of("UID", "one", "val", "A"), lookup.getEntry("1", "one"));
      assertEquals(
          List.of("dm:entry:1241:1:one", "dm:entry:1241:1:three", "dm:entry:1241:1:two"),
          lookup.getPrimaryKeys().stream().sorted().collect(Collectors.toList()));
    } finally {
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testCantChangeDataMapDuringMigration() {
    RedisAccessor redisAccessor = RedisUtils.loadRedis(REDIS_CREDS);
    Lookup lookup = new RedisLookup(redisAccessor, 1151, 300, 500);
    try {
      lookup.save(
          dataMap(
              lookup.getId(),
              V1,
              List.of(
                  Map.of("UID", "one", "val", "A"),
                  Map.of("UID", "two", "val", "B"),
                  Map.of("UID", "three")),
              false));
      JEDIS.hset(
          "dm:master:" + lookup.getId(),
          MIGRATION_STARTED_AT,
          DateTimeFormatter.ISO_INSTANT.format(Instant.now()));
      assertThrows(
          LookupMigrationException.class,
          () -> lookup.setEntry("one", Map.of("UID", "one", "val", "X")));
      assertThrows(
          LookupMigrationException.class,
          () -> lookup.save(dataMap(1151, V2, List.of(Map.of("UID", "one", "val", "A")), false)));
      assertThrows(LookupMigrationException.class, lookup::deleteAsync);
      assertThrows(LookupMigrationException.class, lookup::beginUpdateEntries);
      assertThrows(LookupMigrationException.class, lookup::endUpdateEntries);
      assertThrows(LookupMigrationException.class, () -> lookup.deleteEntry("one"));
      assertThrows(
          LookupMigrationException.class,
          () -> lookup.updateEntries(List.of(Map.of("UID", "one", "val", "A"))));
    } finally {
      JEDIS.hdel("dm:master:" + lookup.getId(), MIGRATION_STARTED_AT);
      lookup.deleteAsync().get();
    }
  }

  @Test
  @SneakyThrows
  public void testMigrateEmptyDataMap_WithoutVersioning() {
    Lookup lookup = LookupUtils.newLookup(REDIS_CREDS, 1141);
    try {
      lookup.save(dataMap(lookup.getId(), V1, List.of(), false));
      assertEquals(V1, lookup.getMasterEntry().dataModelVersion);

      lookup.migrateDataModelVersion(V1, V2);

      var masterEntry = lookup.getMasterEntry();
      assertEquals(V2, masterEntry.dataModelVersion);
      assertEquals(0, masterEntry.mapSize);
      assertFalse(masterEntry.useVersioning);
      assertEquals(0, lookup.countEntries("1", lookup.getMasterEntry().dataModelVersion));
      assertEquals(0, JEDIS.exists("dm:entries:" + lookup.getId()));
      assertEquals(List.of(), lookup.getPrimaryKeys());
    } finally {
      lookup.deleteAsync().get();
    }
  }
}
