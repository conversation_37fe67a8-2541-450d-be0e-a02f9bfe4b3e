package com.nexla.redis;

import com.github.rholder.retry.Retryer;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LookupUtils {

  public static final Logger LOGGER = LoggerFactory.getLogger(LookupUtils.class);

  private LookupUtils() {}

  public static Lookup newLookup(RedisCreds redisCreds, Integer mapId) {
    RedisUtils.RedisLoadingCacheKey cacheKey = new RedisUtils.RedisLoadingCacheKey(redisCreds);
    RedisAccessor accessor = RedisUtils.getRedisAccessorFromCache(cacheKey);
    return new MetricsProvidingLookup(new RedisLookup(accessor, mapId));
  }

  @SneakyThrows
  public static <U> U withLookup(RedisCreds redisCreds, int mapId, Function<Lookup, U> function) {
    RedisUtils.RedisLoadingCacheKey cacheKey = new RedisUtils.RedisLoadingCacheKey(redisCreds);
    Retryer<U> retryer = RedisUtils.buildRetryer(redisCreds, cacheKey);

    return retryer.call(
        () -> {
          RedisAccessor accessor = RedisUtils.getRedisAccessorFromCache(cacheKey);
          var lookup = new MetricsProvidingLookup(new RedisLookup(accessor, mapId));
          return function.apply(lookup);
        });
  }

  @SneakyThrows
  public static void withLookup(RedisCreds redisCreds, int mapId, Consumer<Lookup> consumer) {
    withLookup(
        redisCreds,
        mapId,
        (Function<Lookup, Void>)
            lookup -> {
              consumer.accept(lookup);
              return null;
            });
  }

  @SneakyThrows
  public static void withLookupAndRedis(
      RedisCreds redisCreds, int mapId, BiConsumer<Lookup, RedisAccessor> consumer) {
    RedisUtils.RedisLoadingCacheKey cacheKey = new RedisUtils.RedisLoadingCacheKey(redisCreds);
    Retryer<Void> retryer = RedisUtils.buildRetryer(redisCreds, cacheKey);

    retryer.call(
        () -> {
          RedisAccessor accessor = RedisUtils.getRedisAccessorFromCache(cacheKey);
          var lookup = new MetricsProvidingLookup(new RedisLookup(accessor, mapId));
          consumer.accept(lookup, accessor);
          return null;
        });
  }
}
