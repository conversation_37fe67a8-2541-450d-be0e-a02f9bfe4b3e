package com.nexla.redis;

import io.lettuce.core.internal.LettuceAssert;
import io.lettuce.core.metrics.CommandLatencyId;
import io.lettuce.core.metrics.CommandLatencyRecorder;
import io.lettuce.core.metrics.MicrometerOptions;
import io.lettuce.core.protocol.ProtocolKeyword;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.netty.channel.local.LocalAddress;
import java.net.SocketAddress;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * It is based on {@link io.lettuce.core.metrics.MicrometerCommandLatencyRecorder} and only records
 * end to end command latency for a redis command which are tracked
 */
public class CompletionOnlyLatencyRecorder implements CommandLatencyRecorder {
  static final String METRIC_COMPLETION = "lettuce.command.completion";
  private final MeterRegistry meterRegistry;
  private final MicrometerOptions options;
  private final Map<CommandLatencyId, Timer> completionTimers = new ConcurrentHashMap();

  public CompletionOnlyLatencyRecorder(MeterRegistry meterRegistry, MicrometerOptions options) {
    LettuceAssert.notNull(meterRegistry, "MeterRegistry must not be null");
    LettuceAssert.notNull(options, "MicrometerOptions must not be null");
    this.meterRegistry = meterRegistry;
    this.options = options;
  }

  public void recordCommandLatency(
      SocketAddress local,
      SocketAddress remote,
      ProtocolKeyword protocolKeyword,
      long firstResponseLatency,
      long completionLatency) {
    if (this.isEnabled()) {
      CommandLatencyId commandLatencyId = this.createId(local, remote, protocolKeyword);
      Timer completionTimer =
          this.completionTimers.computeIfAbsent(commandLatencyId, this::completionTimer);
      completionTimer.record(completionLatency, TimeUnit.NANOSECONDS);
    }
  }

  public boolean isEnabled() {
    return this.options.isEnabled();
  }

  private CommandLatencyId createId(
      SocketAddress local, SocketAddress remote, ProtocolKeyword commandType) {
    return CommandLatencyId.create(
        this.options.localDistinction() ? local : LocalAddress.ANY, remote, commandType);
  }

  protected Timer completionTimer(CommandLatencyId commandLatencyId) {
    Timer.Builder timer =
        Timer.builder(METRIC_COMPLETION)
            .description(
                "Latency between command send and command completion (complete response received")
            .tag("command", commandLatencyId.commandType().name())
            .tag("local", commandLatencyId.localAddress().toString())
            .tag("remote", commandLatencyId.remoteAddress().toString())
            .tags(this.options.tags());
    if (this.options.isHistogram()) {
      timer
          .publishPercentileHistogram()
          .publishPercentiles(this.options.targetPercentiles())
          .minimumExpectedValue(this.options.minLatency())
          .maximumExpectedValue(this.options.maxLatency());
    }

    return timer.register(this.meterRegistry);
  }
}
