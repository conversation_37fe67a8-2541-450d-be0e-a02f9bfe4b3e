package com.nexla.redis;

import io.lettuce.core.KeyScanCursor;
import io.lettuce.core.RedisFuture;
import io.lettuce.core.ScanArgs;
import io.lettuce.core.ScanCursor;
import io.lettuce.core.ValueScanCursor;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.cluster.api.async.RedisAdvancedClusterAsyncCommands;
import io.lettuce.core.cluster.api.sync.RedisAdvancedClusterCommands;
import io.lettuce.core.support.ConnectionPoolSupport;
import java.time.Duration;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RedisClusterAccessor implements RedisAccessor {

  public static final Logger logger = LoggerFactory.getLogger(RedisClusterAccessor.class);
  private final GenericObjectPool<StatefulRedisClusterConnection<String, String>> pool;

  public RedisClusterAccessor(RedisClusterClient clusterClient) {
    GenericObjectPoolConfig<StatefulRedisClusterConnection<String, String>> poolConfig =
        new GenericObjectPoolConfig<>();
    poolConfig.setMaxTotal(100);

    this.pool = ConnectionPoolSupport.createGenericObjectPool(clusterClient::connect, poolConfig);
  }

  @Override
  public Boolean exists(String key) {
    return withJedis(jedis -> jedis.exists(key) > 0);
  }

  @Override
  public Map<String, String> hgetAll(String key) {
    return withJedis(jedis -> jedis.hgetall(key));
  }

  @Override
  public String hget(String mapKey, String fieldKey) {
    return withJedis(jedis -> jedis.hget(mapKey, fieldKey));
  }

  @Override
  public Long hdel(String mapKey, String fieldKey) {
    return withJedis(jedis -> jedis.hdel(mapKey, fieldKey));
  }

  @Override
  public Long hset(String mapKey, Map<String, String> map) {
    return withJedis(jedis -> jedis.hset(mapKey, map));
  }

  @Override
  public Long del(String key) {
    return withJedis(jedis -> jedis.del(key));
  }

  @Override
  public Long del(String[] keys) {
    return withJedis(jedis -> jedis.del(keys));
  }

  @Override
  public RedisFuture<Long> delAsync(String[] keys) {
    return withAsyncJedis(jedis -> jedis.del(keys));
  }

  @Override
  public boolean hset(String mapKey, String fieldKey, String fieldValue) {
    return withJedis(jedis -> jedis.hset(mapKey, fieldKey, fieldValue));
  }

  @Override
  public StreamEx<String> scan(ScanArgs params) {
    UnaryOperator<KeyScanCursor<String>> continueOp =
        scanResult -> withJedis(jedis -> jedis.scan(scanResult, params));

    return StreamEx.iterate(withJedis(jedis -> jedis.scan(params)), continueOp)
        .takeWhileInclusive(scanResult -> !scanResult.isFinished())
        .flatMap(stringScanResult -> stringScanResult.getKeys().stream());
  }

  @Override
  public RedisFuture<KeyScanCursor<String>> scanAsync(ScanArgs params) {
    return withAsyncJedis(jedis -> jedis.scan(params));
  }

  @Override
  public RedisFuture<KeyScanCursor<String>> scanAsync(ScanArgs params, ScanCursor cursor) {
    return withAsyncJedis(jedis -> jedis.scan(cursor, params));
  }

  @Override
  public Long lpush(String queue, String entryKey) {
    return withJedis(jedis -> jedis.lpush(queue, entryKey));
  }

  @Override
  public Long lpush(String queue, String[] entryKeys) {
    return withJedis(jedis -> jedis.lpush(queue, entryKeys));
  }

  @Override
  public Long hincrBy(String key, String field, int value) {
    return withJedis(jedis -> jedis.hincrby(key, field, value));
  }

  @Override
  public String get(String key) {
    return withJedis(jedis -> jedis.get(key));
  }

  @Override
  public void set(String key, String value) {
    withJedis(jedis -> jedis.set(key, value));
  }

  @Override
  public void psetex(String key, String value, long l) {
    withJedis(jedis -> jedis.psetex(key, l, value));
  }

  @Override
  public String rpop(String queueName) {
    return withJedis(jedis -> jedis.rpop(queueName));
  }

  @Override
  public boolean hexists(String key, String field) {
    return withJedis(jedis -> jedis.hexists(key, field));
  }

  @Override
  public String lindex(String key, long index) {
    return withJedis(jedis -> jedis.lindex(key, index));
  }

  @Override
  public Long sadd(String mapKey, String... values) {
    return withJedis(jedis -> jedis.sadd(mapKey, values));
  }

  @Override
  public Long scard(String mapKey) {
    return withJedis(jedis -> jedis.scard(mapKey));
  }

  @Override
  public Set<String> smembers(String mapKey) {
    return withJedis(jedis -> jedis.smembers(mapKey));
  }

  @Override
  public StreamEx<String> sscan(String key, ScanArgs params) {
    UnaryOperator<ValueScanCursor<String>> continueOp =
        scanResult -> withJedis(jedis -> jedis.sscan(key, scanResult, params));

    return StreamEx.iterate(withJedis(jedis -> jedis.sscan(key, params)), continueOp)
        .takeWhileInclusive(scanResult -> !scanResult.isFinished())
        .flatMap(stringScanResult -> stringScanResult.getValues().stream());
  }

  @Override
  public RedisFuture<ValueScanCursor<String>> sscanAsync(String key, ScanArgs params) {
    return withAsyncJedis(jedis -> jedis.sscan(key, params));
  }

  @Override
  public Boolean expire(String key, Duration duration) {
    return withJedis(jedis -> jedis.expire(key, duration));
  }

  @Override
  public Long srem(String mapKey, String... values) {
    return withJedis(jedis -> jedis.srem(mapKey, values));
  }

  @Override
  public void closeResources() {
    pool.close();
  }

  @Override
  public boolean isClosed() {
    return pool.isClosed();
  }

  @SneakyThrows
  private <T> T withJedis(Function<RedisAdvancedClusterCommands<String, String>, T> fn) {
    try (StatefulRedisClusterConnection<String, String> jedis = pool.borrowObject()) {
      return fn.apply(jedis.sync());
    }
  }

  @SneakyThrows
  private <T> T withAsyncJedis(Function<RedisAdvancedClusterAsyncCommands<String, String>, T> fn) {
    try (StatefulRedisClusterConnection<String, String> jedis = pool.borrowObject()) {
      return fn.apply(jedis.async());
    }
  }
}
