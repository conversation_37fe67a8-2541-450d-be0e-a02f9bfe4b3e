# base image for kafka connectors
FROM nexla/base-connector:3.3.0-kafka-connect-7.9.0-latest
MAINTAINER Avinash "<EMAIL>"

USER appuser

COPY /base-connector-sink-agent/target/*.jar /app/agent.jar
COPY /base-connector-sink-agent/docker/lifecycle_hook.sh /scripts/lifecycle_hook.sh
COPY /docker/connect-runtime-7.9.0-ce.jar /usr/share/java/confluent-control-center/connect-runtime-7.9.0-ce.jar
COPY /docker/connect-runtime-7.9.0-ccs.jar /usr/share/java/kafka/connect-runtime-7.9.0-ccs.jar
