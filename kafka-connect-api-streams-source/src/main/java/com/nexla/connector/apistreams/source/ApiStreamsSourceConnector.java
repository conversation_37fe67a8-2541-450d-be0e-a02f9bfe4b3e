package com.nexla.connector.apistreams.source;

import com.nexla.connect.common.connector.BaseSourceConnector;
import com.nexla.connector.config.api_streams.ApiStreamsSourceConnectorConfig;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.connector.Task;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Collections.singletonList;


public class ApiStreamsSourceConnector extends BaseSourceConnector<ApiStreamsSourceConnectorConfig> {
	@Override
	protected String telemetryAppName() {
		return "api-streams-source";
	}

	@Override
	protected ApiStreamsSourceConnectorConfig parseConfig(Map<String, String> props) {
		return new ApiStreamsSourceConnectorConfig(props);
	}

	@Override
	public Class<? extends Task> taskClass() {
		return ApiStreamsSourceTask.class;
	}

	@Override
	public ConfigDef config() {
		return ApiStreamsSourceConnectorConfig.configDef();
	}

	@Override
	public List<Map<String, String>> taskConfigs(int maxTasks) {
		return singletonList(new HashMap<>(config.originalsStrings()));
	}
}
