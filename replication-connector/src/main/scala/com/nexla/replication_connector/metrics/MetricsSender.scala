package com.nexla.replication_connector.metrics

import akka.stream.scaladsl.{Flow, Sink}
import com.nexla.admin.client.{DataSet, DataSink, DataSource}
import com.nexla.common.ResourceType
import com.nexla.common.datetime.DateTimeUtils
import com.nexla.common.exception.{NexlaError, NexlaErrorMessage}
import com.nexla.connect.common.NexlaConnectorUtils
import com.nexla.connector.config.FlowType
import com.nexla.replication_connector.connectors.sinks.ReplicationSinkConnector.{SinkUploadError, SinkUploadResult, SinkUploadSuccess}
import com.nexla.replication_connector.messaging.ReplicationMessageProducer
import com.nexla.replication_connector.pipeline.SourceDownloader.{LocalReplicationError, LocallyReplicatedFile, SourceDownloadResult}

import java.util.Optional
import java.util.Optional.empty
import scala.concurrent.{ExecutionContext, Future}


class MetricsSender(messageProducer: ReplicationMessageProducer)(kafkaExecutionContext: ExecutionContext) {
  private implicit val ex: ExecutionContext = kafkaExecutionContext

  private def publishMetric(resourceId: Int, resourceType: ResourceType, orgId: Int, ownerId: Int, dataSetId: Int, runId: Long, recordCount: Long, errorCount: Long, fileSize: Long, fileName: String): Future[Unit] = Future {
    NexlaConnectorUtils.publishMetrics(messageProducer, resourceType, resourceId, fileName, recordCount, fileSize, errorCount,
      DateTimeUtils.nowUTC().getMillis, Optional.of(runId), Optional.of(true), empty(), empty(), Optional.of(dataSetId),
      empty(), empty(), empty(), empty(), FlowType.REPLICATION, orgId, ownerId)
  }

  private def publishErrorMetric(resourceId: Int, resourceType: ResourceType, orgId: Int, ownerId: Int, dataSetId: Int, runId: Long, recordCount: Long, fileName: String, err: Throwable): Future[Unit] = Future {
    val errorMessage = new NexlaErrorMessage(err, "", empty())
    NexlaConnectorUtils.publishMetrics(messageProducer, resourceType, resourceId, fileName, recordCount, 0, 1,
      DateTimeUtils.nowUTC().getMillis, Optional.of(runId), Optional.of(true), empty(), empty(), Optional.of(dataSetId),
      empty(), empty(), Optional.of(errorMessage), empty(), FlowType.REPLICATION, orgId, ownerId)
    NexlaConnectorUtils.publishException(messageProducer, runId, resourceType, resourceId, recordCount, err.getMessage, NexlaError.getErrorDetails(err, Optional.of(java.lang.Long.valueOf(recordCount))))
  }

  def sourceMetrics(dataSource: DataSource, dataSet: DataSet, runId: Long): Sink[SourceDownloadResult, Unit] =
    Flow[SourceDownloadResult]
      .mapAsync(1) {
        case s: LocallyReplicatedFile => publishMetric(dataSource.getId, ResourceType.SOURCE, dataSource.getOrgId(), dataSource.getOwnerId(), dataSet.getId, runId, s.recordCount, 0, s.byteSize, s.displayName)
        case f: LocalReplicationError => publishErrorMetric(dataSource.getId, ResourceType.SOURCE, dataSource.getOrgId(), dataSource.getOwnerId(), dataSet.getId, runId, f.recordCount, f.displayName, f.ex)
      }
      .to(Sink.ignore)
      .mapMaterializedValue(_ => ())


  def sinkMetrics(dataSink: DataSink, dataSet: DataSet, runId: Long): Sink[SinkUploadResult, Unit] =
    Flow[SinkUploadResult]
      .mapAsync(1) {
        case s: SinkUploadSuccess => publishMetric(dataSink.getId, ResourceType.SINK, dataSink.getOrgId(), dataSink.getOwnerId(), dataSet.getId, runId, s.recordCount, s.errorCount, s.byteSize, s.displayName)
        case f: SinkUploadError => publishErrorMetric(dataSink.getId, ResourceType.SINK, dataSink.getOrgId(), dataSink.getOwnerId(), dataSet.getId, runId, f.recordCount, f.displayName, f.ex)
      }
      .to(Sink.ignore)
      .mapMaterializedValue(_ => ())

}
