package com.nexla.replication_connector.pipeline.metadata

import akka.NotUsed
import akka.stream.scaladsl.Source
import com.nexla.connector.config.file.FileSourceConnectorConfig
import com.nexla.file.service.FileConnectorService


case class RemoteReplicationMetadata(source: Source[Seq[FileToReplicate], NotUsed],
                                     sourceConfig: FileSourceConnectorConfig,
                                     probeService: FileConnectorService[_],
                                    )
