package com.nexla.inmemory_connector_common.probe

import com.nexla.admin.client.AdminApiClient
import com.nexla.common.ConnectionType
import com.nexla.file.service.FileConnectorService
import com.nexla.listing.client.{FileVaultClient, ListingClient}

trait ProbeFactory {
  def getProbeService(adminApiClient: AdminApiClient,
                      listingClient: ListingClient,
                      decryptKey: String,
                      sourceType: ConnectionType,
                      fileVaultClient: FileVaultClient): FileConnectorService[_]
}
