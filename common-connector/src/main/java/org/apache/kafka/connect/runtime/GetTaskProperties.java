package org.apache.kafka.connect.runtime;

import lombok.SneakyThrows;
import org.apache.kafka.connect.sink.SinkTaskContext;
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.source.SourceTaskContext;
import org.apache.kafka.connect.util.ConnectorTaskId;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;

public class GetTaskProperties {

	private static final Logger LOGGER = LoggerFactory.getLogger(GetTaskProperties.class);

	@SneakyThrows
	public Integer getTaskId(SinkTaskContext context) {
		try {
			if (context instanceof WorkerSinkTaskContext) {
				WorkerSinkTaskContext sinkTaskContext = (WorkerSinkTaskContext) context;
				Field privateField = WorkerSinkTaskContext.class.getDeclaredField("sinkTask");
				privateField.setAccessible(true);
				WorkerSinkTask fieldValue = (WorkerSinkTask) privateField.get(sinkTaskContext);
				return fieldValue.id.task();
			} else {
				// different classloaders, empty sink task context where we are not connected to kafka, etc
				return null;
			}
		} catch (NoClassDefFoundError e) {
			// same as above
			return null;
		}
	}

	@SneakyThrows
	public Integer getTaskId(SourceTaskContext context) {
		if (context instanceof WorkerSourceTaskContext) {
			WorkerSourceTaskContext taskContext = (WorkerSourceTaskContext) context;
			Field idField = WorkerSourceTaskContext.class.getDeclaredField("id");
			idField.setAccessible(true);
			ConnectorTaskId id = (ConnectorTaskId) idField.get(taskContext);
			return id.task();
		} else {
			// different classloaders, empty sink task context where we are not connected to kafka, etc
			return null;
		}
	}

	@SneakyThrows
	public Optional<Supplier<SourceFlushStatus>> getSourceFlushStatusCalculator(SourceTaskContext context) {
		try {
			if (context instanceof WorkerSourceTaskContext) {
				WorkerSourceTaskContext taskContext = (WorkerSourceTaskContext) context;
				return Optional.of(() -> getSourceFlushStatus(taskContext.transactionContext()));
			} else {
				return Optional.empty();
			}
		} catch (Exception e) {
			LOGGER.error("GetTaskProperties error", e);
			return Optional.empty();
		}
	}

	@NotNull
	@SneakyThrows
	private static SourceFlushStatus getSourceFlushStatus(WorkerTransactionContext workerTransactionContext) {
		int committable = 0;
		int abortable = 0;

		if (workerTransactionContext != null) {
			Field committableRecordsField = WorkerTransactionContext.class.getDeclaredField("committableRecords");
			committableRecordsField.setAccessible(true);
			Field abortableRecordsField = WorkerTransactionContext.class.getDeclaredField("abortableRecords");
			abortableRecordsField.setAccessible(true);

			Set<SourceRecord> committableRecords = (Set<SourceRecord>) committableRecordsField.get(workerTransactionContext);
			Set<SourceRecord> abortableRecords = (Set<SourceRecord>) abortableRecordsField.get(workerTransactionContext);
			committable = committableRecords.size();
			abortable = abortableRecords.size();
		}

		return new SourceFlushStatus(committable, abortable);
	}

}
