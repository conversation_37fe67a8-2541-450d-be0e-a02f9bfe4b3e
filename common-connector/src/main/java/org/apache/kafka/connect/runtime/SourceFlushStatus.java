package org.apache.kafka.connect.runtime;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
public class SourceFlushStatus {

	private final static Logger LOGGER = LoggerFactory.getLogger(SourceFlushStatus.class);

	public final Integer committable;
	public final Integer abortable;

	public boolean allFlushed() {
		LOGGER.info("SourceFlushStatus committable={} abortable={}}",
				committable, abortable);
		return committable + abortable == 0;
	}
}
