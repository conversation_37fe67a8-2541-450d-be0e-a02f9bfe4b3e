package com.nexla.connector.kinesis.sink;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.kinesis.producer.KinesisProducer;
import com.amazonaws.services.kinesis.producer.KinesisProducerConfiguration;
import com.nexla.common.ConnectionType;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.connector.telemetry.SinkTelemetryReporter;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.file.AWSAuthConfig;
import com.nexla.connector.config.kinesis.KinesisSinkConnectorConfig;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

import static com.bazaarvoice.jolt.JsonUtils.toJsonString;
import static com.nexla.common.FileUtils.closeSilently;
import static java.nio.charset.StandardCharsets.UTF_8;

public class KinesisSinkTask extends BaseSinkTask<KinesisSinkConnectorConfig> {

	private KinesisProducer producer;

	@Override
	public void doStart() throws ConnectException {
		this.sinkTelemetryReporter = Optional.of(new SinkTelemetryReporter(config.sinkId, ConnectionType.KINESIS.name()));
		this.producer = getKinesisProducer();
	}

	@Override
	protected ConfigDef configDef() {
		return KinesisSinkConnectorConfig.configDef();
	}

	@Override
	protected void doPut(StreamEx<NexlaMessageContext> messages, int streamSize) {
		ArrayList<Long> dataBufferSizes = new ArrayList<>(streamSize);
		messages.forEach(mes -> {
			// Kinesis does not allow empty partition key
			String partitionKey = partitionKey(mes);
			// If configured use kafka partition key as explicit hash key
			// This will be useful when sending data from same partition into
			// same shard
			String hashKey = config.usePartitionAsHashKey ? mes.topicPartition.partition + "" : null;
			ByteBuffer dataBuffer = toDataBuffer(mes.mapped.getRawMessage());
			dataBufferSizes.add(Long.valueOf(dataBuffer.remaining()));
			producer.addUserRecord(config.streamName, partitionKey, hashKey, dataBuffer);
		});
		producer.flushSync();
		sendMetric(config.sinkId + ":" + config.streamName, Optional.empty(), streamSize,
			dataBufferSizes.stream().reduce(0L, Long::sum), 0L);
	}

	private String partitionKey(NexlaMessageContext m) {
		return m.topicPartition.partition + "";
	}

	@SneakyThrows
	private ByteBuffer toDataBuffer(LinkedHashMap<String, Object> rawMessage) {
		return ByteBuffer.wrap((toJsonString(rawMessage)).getBytes(UTF_8));
	}

	private KinesisProducer getKinesisProducer() {
		KinesisProducerConfiguration producerConf = new KinesisProducerConfiguration();
		producerConf.setRegion(config.region);

		AWSAuthConfig authConfig = config.authConfig;
		AWSStaticCredentialsProvider credentialsProvider =
			new AWSStaticCredentialsProvider(
				new BasicAWSCredentials(authConfig.accessKeyId, authConfig.secretKey));

		producerConf.setCredentialsProvider(credentialsProvider);
		producerConf.setMaxConnections(config.maxConnections);

		producerConf.setAggregationEnabled(config.aggregationEnabled);

		// Limits the maximum allowed put rate for a shard, as a percentage of
		// the
		// backend limits.
		producerConf.setRateLimit(config.rateLimit);

		// Maximum amount of time (milliseconds) a record may spend being
		// buffered
		// before it gets sent. Records may be sent sooner than this depending
		// on the
		// other buffering limits
		producerConf.setRecordMaxBufferedTime(config.maxBufferedTime);

		// Set a time-to-live on records (milliseconds). Records that do not get
		// successfully put within the limit are failed.
		producerConf.setRecordTtl(config.ttl);

		// Controls the number of metrics that are uploaded to CloudWatch.
		// Expected pattern: none|summary|detailed
		producerConf.setMetricsLevel(config.metricsLevel);

		// Controls the granularity of metrics that are uploaded to CloudWatch.
		// Greater granularity produces more metrics.
		// Expected pattern: global|stream|shard
		producerConf.setMetricsGranularity(config.metricsGranuality);

		// The namespace to upload metrics under.
		producerConf.setMetricsNamespace(config.metricsNameSpace);

		return new KinesisProducer(producerConf);
	}

	@Override
	protected KinesisSinkConnectorConfig parseConfig(Map<String, String> props) {
		return new KinesisSinkConnectorConfig(props);
	}

	@Override
	public void stop() {
		closeSilently(producer::destroy);
		super.stop();
	}
}
