package com.nexla.telemetry;

import static java.util.concurrent.TimeUnit.MINUTES;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalListener;
import com.google.common.cache.RemovalNotification;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.nexla.telemetry.dto.*;
import io.micrometer.core.instrument.Clock;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.prometheus.PrometheusConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import io.prometheus.client.*;
import io.prometheus.client.exporter.PushGateway;
import io.prometheus.client.hotspot.DefaultExports;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.BiConsumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import lombok.Data;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PrometheusTelemetry implements Telemetry {
  private static final Logger LOGGER = LoggerFactory.getLogger(PrometheusTelemetry.class);
  private static final List<String> SYSTEM_LABELS = List.of("env_type", "app_name");

  private static final int PUSHGATEWAY_REPORT_FREQ = 15; // 15 seconds
  private static final int CACHE_EXPIRATION = 1560; // 26 hours
  private static final int CACHE_MAX_SIZE = 1000;
  public static final CollectorRegistry COLLECTOR_REGISTRY = CollectorRegistry.defaultRegistry;
  private final MeterRegistry meterRegistry =
      new PrometheusMeterRegistry(
          PrometheusConfig.DEFAULT, PrometheusTelemetry.COLLECTOR_REGISTRY, Clock.SYSTEM);

  static {
    DefaultExports.register(COLLECTOR_REGISTRY);
  }

  @Data
  static class PushKey {
    private final String metricName;
    private final Map<String, String> gatewayGroupingKey;
  }

  private static class DeregistrationRemovalListener<K, T extends Collector>
      implements RemovalListener<K, T> {
    @Override
    public void onRemoval(RemovalNotification<K, T> notification) {
      COLLECTOR_REGISTRY.unregister(notification.getValue());
    }
  }

  // different kind of gauge cache, because it will not be exposed here - instead it will be done on
  // pushgw
  private final Cache<PushKey, Gauge> PUSH_GAUGES =
      CacheBuilder.newBuilder()
          .expireAfterAccess(CACHE_EXPIRATION, MINUTES)
          .maximumSize(CACHE_MAX_SIZE)
          .removalListener(new DeregistrationRemovalListener<PushKey, Gauge>())
          .build();
  private final Cache<PushKey, Counter> PUSH_COUNTERS =
      CacheBuilder.newBuilder()
          .expireAfterAccess(CACHE_EXPIRATION, MINUTES)
          .maximumSize(CACHE_MAX_SIZE)
          .removalListener(new DeregistrationRemovalListener<PushKey, Counter>())
          .build();
  private final Cache<PushKey, Histogram> PUSH_HISTOGRAMS =
      CacheBuilder.newBuilder()
          .expireAfterAccess(CACHE_EXPIRATION, MINUTES)
          .maximumSize(CACHE_MAX_SIZE)
          .removalListener(new DeregistrationRemovalListener<PushKey, Histogram>())
          .build();
  private final Cache<String, Gauge> GAUGES =
      CacheBuilder.newBuilder()
          .expireAfterAccess(CACHE_EXPIRATION, MINUTES)
          .maximumSize(CACHE_MAX_SIZE)
          .removalListener(new DeregistrationRemovalListener<String, Gauge>())
          .build();
  private final Cache<String, Counter> COUNTERS =
      CacheBuilder.newBuilder()
          .expireAfterAccess(CACHE_EXPIRATION, MINUTES)
          .maximumSize(CACHE_MAX_SIZE)
          .removalListener(new DeregistrationRemovalListener<String, Counter>())
          .build();
  private final Cache<String, Histogram> HISTOGRAMS =
      CacheBuilder.newBuilder()
          .expireAfterAccess(CACHE_EXPIRATION, MINUTES)
          .maximumSize(CACHE_MAX_SIZE)
          .removalListener(new DeregistrationRemovalListener<String, Histogram>())
          .build();

  // No expiration maps are  used to keep closable Nexla gauges, counters and histograms.
  private final ConcurrentHashMap<String, Gauge> NO_EXPIRATION_GAUGES = new ConcurrentHashMap<>();
  private final ConcurrentHashMap<String, Counter> NO_EXPIRATION_COUNTERS =
      new ConcurrentHashMap<>();
  private final ConcurrentHashMap<String, Histogram> NO_EXPIRATION_HISTOGRAMS =
      new ConcurrentHashMap<>();

  private final List<String> systemLabelValues;
  @Getter private final PrometheusPushgatewayPushManager pushManager;

  public PrometheusTelemetry(String envType, String appName) {
    this.systemLabelValues = List.of(envType, appName);
    this.pushManager = null;
    LOGGER.info("Prometheus is setup, push metrics reporting is disabled");
  }

  public PrometheusTelemetry(String envType, String appName, String pushGwUrl) {
    this.systemLabelValues = List.of(envType, appName);
    if (pushGwUrl != null && !pushGwUrl.isEmpty()) {
      this.pushManager = new PrometheusPushgatewayPushManager(pushGwUrl);
      LOGGER.info("Prometheus is setup, pushGateway URL {}", pushGwUrl);
    } else {
      this.pushManager = null;
      LOGGER.warn("Prometheus is setup, provided pushGateway URL was empty");
    }
  }

  @Override
  public MeterRegistry getMeterRegistry() {
    return meterRegistry;
  }

  @Override
  public NexlaGauge getGauge(String name, List<Label> labels) {
    try {
      String promName = cleansePromNames(name);
      Gauge gauge =
          NO_EXPIRATION_GAUGES.computeIfAbsent(
              promName, (ignored) -> createGauge(promName, convertLabelNames(labels)));
      String[] childLabelValues = convertLabelValues(labels);
      Gauge.Child child = gauge.labels(childLabelValues);
      return new NexlaGauge() {
        @Override
        public void set(double val) {
          child.set(val);
        }

        @Override
        public void close() {
          gauge.remove(childLabelValues);
        }

        @Override
        public void closeAndUnregister() {
          close();
          Gauge removed = NO_EXPIRATION_GAUGES.remove(promName);
          if (removed != null) {
            COLLECTOR_REGISTRY.unregister(removed);
          } else {
            LOGGER.warn(
                "Trying to close gauge that was already closed, name={}, labels={}", name, labels);
          }
        }
      };
    } catch (Exception e) {
      LOGGER.error("Failed to getGauge ({})", name, e);
      return NoopTelemetry.NOOP_GAUGE;
    }
  }

  @Override
  public NexlaTimer getGaugeTimer(String name, List<Label> labels) {
    try {
      String promName = cleansePromNames(name);
      Gauge gauge =
          NO_EXPIRATION_GAUGES.computeIfAbsent(
              promName, (ignored) -> createGauge(promName, convertLabelNames(labels)));
      String[] childLabelValues = convertLabelValues(labels);
      Gauge.Child child = gauge.labels(childLabelValues);
      return new NexlaTimer() {
        @Override
        public QuietAutoClosable time() {
          return new QuietAutoClosable(child.startTimer());
        }

        @Override
        public void close() {
          gauge.remove(childLabelValues);
        }

        @Override
        public void closeAndUnregister() {
          close();
          Gauge removed = NO_EXPIRATION_GAUGES.remove(promName);
          if (removed != null) {
            COLLECTOR_REGISTRY.unregister(removed);
          } else {
            LOGGER.warn(
                "Trying to close gauge that was already closed, name={}, labels={}", name, labels);
          }
        }
      };
    } catch (Exception e) {
      LOGGER.error("Failed to getGaugeTimer ({})", name, e);
      return NoopTelemetry.NOOP_TIMER;
    }
  }

  @Override
  public NexlaCounter getCounter(String name, List<Label> labels) {
    try {
      String promName = cleansePromNames(name);
      Counter counter =
          NO_EXPIRATION_COUNTERS.computeIfAbsent(
              promName, (ignored) -> createCounter(promName, convertLabelNames(labels)));
      String[] childLabelValues = convertLabelValues(labels);
      Counter.Child child = counter.labels(childLabelValues);
      return new NexlaCounter() {
        @Override
        public void inc(double val) {
          child.inc(val);
        }

        @Override
        public void close() {
          counter.remove(childLabelValues);
        }

        @Override
        public void closeAndUnregister() {
          close();
          Counter removed = NO_EXPIRATION_COUNTERS.remove(promName);
          if (removed != null) {
            COLLECTOR_REGISTRY.unregister(removed);
          } else {
            LOGGER.warn(
                "Trying to close counter that was already closed, name={}, labels={}",
                name,
                labels);
          }
        }
      };
    } catch (Exception e) {
      LOGGER.error("Failed to getCounter ({})", name, e);
      return NoopTelemetry.NOOP_COUNTER;
    }
  }

  @Override
  public NexlaHistogram getHistogram(String name, List<Label> labels, double[] buckets) {
    try {
      String promName = cleansePromNames(name);
      Histogram histogram =
          NO_EXPIRATION_HISTOGRAMS.computeIfAbsent(
              promName, (ignored) -> createHistogram(promName, convertLabelNames(labels), buckets));
      final String[] childLabelValues = convertLabelValues(labels);
      final Histogram.Child child = histogram.labels(childLabelValues);
      return new NexlaHistogram() {
        @Override
        public void observe(double val) {
          child.observe(val);
        }

        @Override
        public void close() {
          histogram.remove(childLabelValues);
        }

        @Override
        public void closeAndUnregister() {
          close();
          Histogram removed = NO_EXPIRATION_HISTOGRAMS.remove(promName);
          if (removed != null) {
            COLLECTOR_REGISTRY.unregister(removed);
          } else {
            LOGGER.warn(
                "Trying to close histogram that was already closed, name={}, labels={}",
                name,
                labels);
          }
        }
      };
    } catch (Exception e) {
      LOGGER.error("Failed to getHistogram ({})", name, e);
      return NoopTelemetry.NOOP_HISTOGRAM;
    }
  }

  // Fixme method is implemented as a mix of push (with expiration cache) and non-push closable
  // Nexla histograms. Unify the approach.
  @Override
  public NexlaHistogram getPushHistogram(
      String name, List<Label> groupKeys, List<Label> labels, double[] buckets) {
    try {
      String promName = cleansePromNames(name);
      PushKey pushKey = new PushKey(promName, convertGroupKeys(groupKeys));
      Histogram histogram =
          PUSH_HISTOGRAMS.get(
              pushKey, () -> createPushHistogram(promName, convertLabelNames(labels), buckets));
      final Histogram.Child child = histogram.labels(convertLabelValues(labels));
      return new NexlaHistogram() {
        @Override
        public void observe(double val) {
          child.observe(val);
        }

        @Override
        public void close() {
          COLLECTOR_REGISTRY.unregister(histogram);
        }
      };
    } catch (Exception e) {
      LOGGER.error("Failed to getHistogram ({})", name, e);
      return NoopTelemetry.NOOP_HISTOGRAM;
    }
  }

  @Override
  public NexlaTimer getHistogramTimer(String name, List<Label> labels, double[] buckets) {
    try {
      String promName = cleansePromNames(name);
      Histogram histogram =
          NO_EXPIRATION_HISTOGRAMS.computeIfAbsent(
              promName, (ignored) -> createHistogram(promName, convertLabelNames(labels), buckets));
      String[] childLabelValues = convertLabelValues(labels);
      Histogram.Child child = histogram.labels(childLabelValues);
      return new NexlaTimer() {
        @Override
        public QuietAutoClosable time() {
          return new QuietAutoClosable(child.startTimer());
        }

        @Override
        public void close() {
          histogram.remove(childLabelValues);
        }

        @Override
        public void closeAndUnregister() {
          close();
          Histogram removed = NO_EXPIRATION_HISTOGRAMS.remove(promName);
          if (removed != null) {
            COLLECTOR_REGISTRY.unregister(removed);
          } else {
            LOGGER.warn(
                "Trying to close histogram that was already closed, name={}, labels={}",
                name,
                labels);
          }
        }
      };
    } catch (Exception e) {
      LOGGER.error("Failed to getHistogramTimer ({})", name, e);
      return NoopTelemetry.NOOP_TIMER;
    }
  }

  @Override
  public void recordOrPushGauge(
      String name, List<Label> groupKeys, Double value, List<Label> labels) {
    if (this.pushManager != null) {
      pushGauge(name, groupKeys, value, labels);
    } else {
      recordGauge(name, value, labels);
    }
  }

  @Override
  public void recordGauge(String name, double value) {
    recordGauge(name, value, Collections.emptyList());
  }

  @Override
  public void recordGauge(String name, double value, List<Label> labels) {
    try {
      String promName = cleansePromNames(name);
      Gauge gauge = GAUGES.get(promName, () -> createGauge(promName, convertLabelNames(labels)));
      gauge.labels(convertLabelValues(labels)).set(value);
    } catch (Exception e) {
      LOGGER.error("Failed to send prom gauge", e);
    }
  }

  @Override
  public void recordGaugeDuration(String name, List<Label> labels, Runnable runnable) {
    Optional<Gauge.Timer> timer = startGaugeTimer(name, labels);
    try {
      runnable.run();
    } finally {
      timer.ifPresent(Gauge.Timer::close);
    }
  }

  @Override
  public <T> T recordGaugeDuration(String name, List<Label> labels, Supplier<T> supplier) {
    Optional<Gauge.Timer> timer = startGaugeTimer(name, labels);
    try {
      return supplier.get();
    } finally {
      timer.ifPresent(Gauge.Timer::close);
    }
  }

  @Override
  public <T> T recordGaugeDuration(String name, List<Label> labels, Callable<T> callable)
      throws Exception {
    Optional<Gauge.Timer> timer = startGaugeTimer(name, labels);
    try {
      return callable.call();
    } finally {
      timer.ifPresent(Gauge.Timer::close);
    }
  }

  @Override
  public void recordOrPushCounter(String name, List<Label> groupKeys, List<Label> labels) {
    if (this.pushManager != null) {
      pushCounter(name, groupKeys, labels);
    } else {
      recordCounter(name, labels);
    }
  }

  @Override
  public void recordCounter(String name) {
    recordCounter(name, 1.0, Collections.emptyList());
  }

  @Override
  public void recordCounter(String name, double value) {
    recordCounter(name, value, Collections.emptyList());
  }

  @Override
  public void recordCounter(String name, List<Label> labels) {
    recordCounter(name, 1.0, labels);
  }

  @Override
  public void recordCounter(String name, double value, List<Label> labels) {
    try {
      String promName = cleansePromNames(name);
      Counter counter =
          COUNTERS.get(promName, () -> createCounter(promName, convertLabelNames(labels)));
      counter.labels(convertLabelValues(labels)).inc(value);
    } catch (Exception e) {
      LOGGER.error("Failed to send prom counter", e);
    }
  }

  @Override
  public void recordHistogram(String name, double value) {
    recordHistogram(name, value, Collections.emptyList());
  }

  @Override
  public void recordHistogram(String name, double value, List<Label> labels) {
    try {
      String promName = cleansePromNames(name);
      Histogram histogram =
          HISTOGRAMS.get(promName, () -> createHistogram(promName, convertLabelNames(labels)));
      histogram.labels(convertLabelValues(labels)).observe(value);
    } catch (Exception e) {
      LOGGER.error("Failed to send prom histogram", e);
    }
  }

  @Override
  public void recordHistogram(String name, double value, List<Label> labels, double... buckets) {
    try {
      String promName = cleansePromNames(name);
      Histogram histogram =
          HISTOGRAMS.get(
              promName, () -> createHistogram(promName, convertLabelNames(labels), buckets));
      histogram.labels(convertLabelValues(labels)).observe(value);
    } catch (Exception e) {
      LOGGER.error("Failed to send prom histogram", e);
    }
  }

  @Override
  public void recordHistogramDuration(String name, List<Label> labels, Runnable runnable) {
    Optional<Histogram.Timer> timer = startHistogramTimer(name, labels);
    try {
      runnable.run();
    } finally {
      timer.ifPresent(Histogram.Timer::close);
    }
  }

  @Override
  public <T> T recordHistogramDuration(String name, List<Label> labels, Supplier<T> supplier) {
    Optional<Histogram.Timer> timer = startHistogramTimer(name, labels);
    try {
      return supplier.get();
    } finally {
      timer.ifPresent(Histogram.Timer::close);
    }
  }

  @Override
  public <T> T recordHistogramDuration(String name, List<Label> labels, Callable<T> callable)
      throws Exception {
    Optional<Histogram.Timer> timer = startHistogramTimer(name, labels);
    try {
      return callable.call();
    } finally {
      timer.ifPresent(Histogram.Timer::close);
    }
  }

  private Optional<Gauge.Timer> startGaugeTimer(String name, List<Label> labels) {
    Gauge.Timer timer = null;
    try {
      String promName = cleansePromNames(name);
      Gauge gauge = GAUGES.get(promName, () -> createGauge(promName, convertLabelNames(labels)));
      timer = gauge.labels(convertLabelValues(labels)).startTimer();
    } catch (Exception e) {
      LOGGER.error("Failed to send gauge duration", e);
    }
    return Optional.ofNullable(timer);
  }

  private Optional<Histogram.Timer> startHistogramTimer(String name, List<Label> labels) {
    Histogram.Timer timer = null;
    try {
      String promName = cleansePromNames(name);
      Histogram histogram =
          HISTOGRAMS.get(promName, () -> createHistogram(promName, convertLabelNames(labels)));
      timer = histogram.labels(convertLabelValues(labels)).startTimer();
    } catch (Exception e) {
      LOGGER.error("Failed to send histogram duration", e);
    }
    return Optional.ofNullable(timer);
  }

  @Override
  public void pushGaugeDuration(
      String name, List<Label> groupKeys, List<Label> labels, Runnable runnable) {
    if (this.pushManager != null) {
      try {
        String promName = cleansePromNames(name);
        PushKey pushKey = new PushKey(promName, convertGroupKeys(groupKeys));
        Gauge gauge =
            PUSH_GAUGES.get(pushKey, () -> createPushGauge(promName, convertLabelNames(labels)));
        gauge.labels(convertLabelValues(labels)).setToTime(runnable);
      } catch (Exception e) {
        LOGGER.error("Failed to send duration", e);
      }
    } else {
      LOGGER.trace("PushGateway URL is empty, could not push duration");
    }
  }

  @Override
  public void pushGaugeDuration(String name, List<Label> groupKeys, Runnable runnable) {
    if (this.pushManager != null) {
      pushGaugeDuration(name, groupKeys, Collections.emptyList(), runnable);
    } else {
      LOGGER.trace("PushGateway URL is empty, could not push duration");
    }
  }

  @Override
  public void pushGauge(String name, List<Label> groupKeys, Double value, List<Label> labels) {
    if (this.pushManager != null) {
      try {
        String promName = cleansePromNames(name);
        PushKey pushKey = new PushKey(promName, convertGroupKeys(groupKeys));
        Gauge gauge =
            PUSH_GAUGES.get(pushKey, () -> createPushGauge(promName, convertLabelNames(labels)));
        gauge.labels(convertLabelValues(labels)).set(value);
      } catch (Exception e) {
        LOGGER.error("Failed to send duration", e);
      }
    } else {
      LOGGER.trace("PushGateway URL is empty, could not push duration");
    }
  }

  @Override
  public void registerPushGauge(String name, List<Label> groupKeys, List<Label> labels) {
    if (this.pushManager != null) {
      try {
        String promName = cleansePromNames(name);
        PushKey pushKey = new PushKey(promName, convertGroupKeys(groupKeys));
        PUSH_GAUGES.get(pushKey, () -> createPushGauge(promName, convertLabelNames(labels)));
      } catch (Exception e) {
        LOGGER.trace("Failed to register gauge metric", e);
      }
    }
  }

  @Override
  public void pushGauge(String name, List<Label> groupKeys, Double value) {
    if (this.pushManager != null) {
      pushGauge(name, groupKeys, value, Collections.emptyList());
    } else {
      LOGGER.trace("PushGateway URL is empty, could not push duration");
    }
  }

  @Override
  public void pushCounter(String name, List<Label> groupKeys, List<Label> labels, Double amount) {
    if (this.pushManager != null) {
      try {
        String promName = cleansePromNames(name);
        PushKey pushKey = new PushKey(promName, convertGroupKeys(groupKeys));
        Counter counter =
            PUSH_COUNTERS.get(
                pushKey, () -> createPushCounter(promName, convertLabelNames(labels)));
        counter.labels(convertLabelValues(labels)).inc(amount);
      } catch (Exception e) {
        LOGGER.error("Failed to send metro", e);
      }
    } else {
      LOGGER.trace("PushGateway URL is empty, could not push counter");
    }
  }

  @Override
  public void pushCounter(String name, List<Label> groupKeys, List<Label> labels) {
    pushCounter(name, groupKeys, labels, 1.0);
  }

  @Override
  public void pushCounter(String name, List<Label> groupKeys) {
    if (this.pushManager != null) {
      pushCounter(name, groupKeys, Collections.emptyList());
    } else {
      LOGGER.trace("PushGateway URL is empty, could not push counter");
    }
  }

  @Override
  public void registerPushCounter(String name, List<Label> groupKeys, List<Label> labels) {
    if (this.pushManager != null) {
      try {
        String promName = cleansePromNames(name);
        PushKey pushKey = new PushKey(promName, convertGroupKeys(groupKeys));
        PUSH_COUNTERS.get(pushKey, () -> createPushCounter(promName, convertLabelNames(labels)));
      } catch (Exception e) {
        LOGGER.trace("Failed to register metric", e);
      }
    }
  }

  @Override
  public Map<String, String> systemLabels() {
    Map<String, String> labelsMap = new LinkedHashMap<>();
    for (int i = 0; i < SYSTEM_LABELS.size(); i++) {
      labelsMap.put(SYSTEM_LABELS.get(i), systemLabelValues.get(i));
    }
    return labelsMap;
  }

  private Gauge createPushGauge(String name, String[] labels) {
    Gauge gauge = Gauge.build().name(name).help(name).labelNames(labels).create();
    return gauge;
  }

  private Counter createPushCounter(String name, String[] labels) {
    Counter counter = Counter.build().name(name).help(name).labelNames(labels).create();
    return counter;
  }

  private Histogram createPushHistogram(String name, String[] labels, double[] buckets) {
    Histogram histogram =
        Histogram.build().name(name).help(name).buckets(buckets).labelNames(labels).create();
    return histogram;
  }

  private Gauge createGauge(String name, String[] labels) {
    Gauge gauge = Gauge.build().name(name).help(name).labelNames(labels).create();
    registerCollector(name, labels, gauge);
    return gauge;
  }

  private Counter createCounter(String name, String[] labels) {
    Counter counter = Counter.build().name(name).help(name).labelNames(labels).create();
    registerCollector(name, labels, counter);
    return counter;
  }

  private Histogram createHistogram(String name, String[] labels) {
    return createHistogram(name, labels, null);
  }

  private Histogram createHistogram(String name, String[] labels, @Nullable double[] buckets) {
    Histogram.Builder histogramBuilder = Histogram.build().name(name).help(name).labelNames(labels);
    if (buckets != null) {
      histogramBuilder.buckets(buckets);
    }
    Histogram histogram = histogramBuilder.create();
    registerCollector(name, labels, histogram);
    return histogram;
  }

  private void registerCollector(String name, String[] labels, Collector collector) {
    try {
      COLLECTOR_REGISTRY.register(collector);
    } catch (Exception e) {
      LOGGER.warn(
          "Failed to register with prom collector registry, class={}, name={}, labels={}",
          collector.getClass().getSimpleName(),
          name,
          Arrays.toString(labels),
          e);
    }
  }

  private String[] convertLabelNames(List<Label> labels) {
    ArrayList<String> mergedLabels = new ArrayList<>(SYSTEM_LABELS);
    labels.forEach(t -> mergedLabels.add(cleansePromNames(t.getName())));
    return mergedLabels.toArray(new String[0]);
  }

  private String[] convertLabelValues(List<Label> labels) {
    ArrayList<String> mergedValues = new ArrayList<>(systemLabelValues);
    labels.forEach(t -> mergedValues.add(t.getValue()));
    return mergedValues.toArray(new String[0]);
  }

  private Map<String, String> convertGroupKeys(List<Label> groupKeys) {
    return groupKeys.stream().collect(Collectors.toMap(x -> x.getName(), x -> x.getValue()));
  }

  private String cleansePromNames(String name) {
    return name.replaceAll("[^a-zA-Z0-9_:]", "_");
  }

  private class PrometheusPushgatewayPushManager {
    private final ScheduledThreadPoolExecutor pushgatewayExecutor =
        new ScheduledThreadPoolExecutor(
            1,
            new ThreadFactoryBuilder()
                .setDaemon(true)
                .setNameFormat("pushgateway-manager-thread")
                .build());
    private final PushGateway pushGw;

    PrometheusPushgatewayPushManager(String pushGwUrl) {
      this.pushGw = new PushGateway(pushGwUrl);

      pushgatewayExecutor.scheduleWithFixedDelay(
          () -> forAllMetric(this::tryPushMetricAsync),
          0,
          PUSHGATEWAY_REPORT_FREQ,
          TimeUnit.SECONDS);

      Thread executorShutdownHook =
          new Thread(
              () -> {
                // try to make sure all metrics are pushed prior to app shutdown
                forAllMetric(this::tryPushMetric);
                LOGGER.info("The last values of metrics sent to Prometheus pushGateway");
              });

      Runtime.getRuntime().addShutdownHook(executorShutdownHook);
    }

    private <T extends Collector> void forAllMetric(
        BiConsumer<? super PushKey, ? super Collector> action) {
      forEachMetric(PUSH_GAUGES, action);
      forEachMetric(PUSH_COUNTERS, action);
      forEachMetric(PUSH_HISTOGRAMS, action);
    }

    private <T extends Collector> void forEachMetric(
        Cache<PushKey, T> metrics, BiConsumer<? super PushKey, ? super Collector> action) {
      ConcurrentMap<PushKey, T> collectors = metrics.asMap();
      collectors.forEach(action);
    }

    private void tryPushMetricAsync(PushKey pushKey, Collector collector) {
      pushgatewayExecutor.submit(() -> tryPushMetric(pushKey, collector));
    }

    private void tryPushMetric(PushKey pushKey, Collector collector) {
      try {
        pushGw.push(collector, pushKey.metricName, pushKey.gatewayGroupingKey);
      } catch (Exception e) {
        LOGGER.trace("Failed to push PushGateway job with prom collector registry", e);
      }
    }
  }
}
