package com.nexla.connector.sql.debezium;

import io.debezium.config.Instantiator;
import org.apache.kafka.connect.json.JsonConverter;
import org.apache.kafka.connect.runtime.WorkerConfig;
import org.apache.kafka.connect.storage.Converter;
import org.apache.kafka.connect.storage.OffsetBackingStore;
import org.apache.kafka.connect.storage.OffsetStorageWriter;
import org.apache.kafka.connect.storage.OffsetUtils;
import org.apache.kafka.connect.util.Callback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

import static org.apache.commons.lang3.concurrent.ConcurrentUtils.constantFuture;
import static org.apache.kafka.connect.json.JsonConverterConfig.SCHEMAS_ENABLE_CONFIG;

/**
 * In-memory store that helps running debezium embedded engine
 */
public class DebeziumMapOffsetStore implements OffsetBackingStore {
	private static final Logger logger = LoggerFactory.getLogger(DebeziumMapOffsetStore.class);
	private final Map<String, Set<Map<String, Object>>> connectorPartitions = new HashMap<>();

	private static final ConcurrentHashMap<ByteBuffer, ByteBuffer> OFFSETS = new ConcurrentHashMap<>();

	private static final Converter KEY_CONVERTER;
	private static final Converter VALUE_CONVERTER;

	static {
		Map<String, String> internalConverterConfig = Collections.singletonMap(SCHEMAS_ENABLE_CONFIG, "false");
		KEY_CONVERTER = (Converter) Instantiator.getInstance(JsonConverter.class.getName());
		KEY_CONVERTER.configure(internalConverterConfig, true);

		VALUE_CONVERTER = (Converter)Instantiator.getInstance(JsonConverter.class.getName());
		VALUE_CONVERTER.configure(internalConverterConfig, false);
	}

	/**
	 * From {@link OffsetStorageWriter#doFlush(org.apache.kafka.connect.util.Callback)}
	 */
	public static void setKeyValue(Map<String, Object> keyMap, Map<String, Object> valueMap, String namespace) {
		OffsetUtils.validateFormat(keyMap);
		OffsetUtils.validateFormat(valueMap);
		// When serializing the key, we add in the namespace information so the key is [namespace, real key]
		byte[] key = KEY_CONVERTER.fromConnectData(namespace, null, Arrays.asList(namespace, keyMap));
		ByteBuffer keyBuffer = (key != null) ? ByteBuffer.wrap(key) : null;
		byte[] value = VALUE_CONVERTER.fromConnectData(namespace, null, valueMap);
		ByteBuffer valueBuffer = (value != null) ? ByteBuffer.wrap(value) : null;
		OFFSETS.put(keyBuffer, valueBuffer);
	}

	public static void removeKey(Map<String, Object> keyMap, String namespace) {
		OffsetUtils.validateFormat(keyMap);
		byte[] key = KEY_CONVERTER.fromConnectData(namespace, null, Arrays.asList(namespace, keyMap));
		ByteBuffer keyBuffer = (key != null) ? ByteBuffer.wrap(key) : null;

		OFFSETS.remove(keyBuffer);
	}

	@Override
	public void start() {

	}

	@Override
	public void stop() {

	}

	@Override
	public Future<Map<ByteBuffer, ByteBuffer>> get(Collection<ByteBuffer> keys) {
		Map<ByteBuffer, ByteBuffer> result = new HashMap<>();
		keys.forEach(key -> result.put(key, OFFSETS.get(key)));
		return constantFuture(result);
	}

	@Override
	public Future<Void> set(Map<ByteBuffer, ByteBuffer> values, Callback<Void> callback) {
		for (Map.Entry<ByteBuffer, ByteBuffer> entry : values.entrySet()) {
			ByteBuffer key = entry.getKey();
			ByteBuffer value = entry.getValue();
			logger.info("Storing ByteBuffer offset from Kafka: {}, value: {}", byteBufferToString(key),  byteBufferToString(value));
		}
		return constantFuture(null);
	}

	@Override
	public Set<Map<String, Object>> connectorPartitions(String connectorName) {
		return this.connectorPartitions.getOrDefault(connectorName, Collections.emptySet());
	}

	private static String byteBufferToString(ByteBuffer buffer) {
		if(buffer != null) {
			// Convert ByteBuffer to byte array
			byte[] bytes = new byte[buffer.remaining()];
			buffer.get(bytes);

			// Create a Charset instance for the desired character encoding
			Charset charset = Charset.forName("UTF-8");

			// Convert byte array to String
			return new String(bytes, charset);
		}
		return "";
	}

	@Override
	public void configure(WorkerConfig config) {
		System.out.println();
	}
}
