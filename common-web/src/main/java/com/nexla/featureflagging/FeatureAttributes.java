package com.nexla.featureflagging;

import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaConstants;
import com.nexla.common.ResourceType;
import com.nexla.connector.config.FlowType;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FeatureAttributes {

  private static final String ID = "_ID";

  private String featureName;
  private Integer orgId;
  private NexlaConstants.ConnectionTypeCategory category;
  private ConnectionType connectionType;
  private ResourceType resourceType;
  private Integer resourceId;
  private Integer flowId;
  private Integer sinkId;
  private Integer sourceId;
  private Integer transformId;
  private FlowType flowType;
  private Integer flowOwnerId;

  public Map<String, String> attributesMap() {
    Map<String, String> attributesJson = new HashMap<>();

    if (this.orgId != null) {
      attributesJson.put(Attributes.ORG_ID.name(), this.orgId.toString());
    }

    if (this.category != null) {
      attributesJson.put(Attributes.CONNECTION_CATEGORY.name(), this.category.name());
    }

    if (this.connectionType != null) {
      attributesJson.put(Attributes.CONNECTION_TYPE.name(), this.connectionType.name());
    }

    if (this.resourceType != null) {
      attributesJson.put(Attributes.RESOURCE_TYPE.name(), this.resourceType.name());
    }

    if (this.resourceId != null) {
      attributesJson.put(Attributes.RESOURCE_ID.name(), this.resourceId.toString());
    }

    if (this.flowId != null) {
      attributesJson.put(Attributes.FLOW_ID.name(), this.flowId.toString());
    }

    if (this.sinkId != null) {
      attributesJson.put(Attributes.SINK_ID.name(), this.sinkId.toString());
    }

    if (this.sourceId != null) {
      attributesJson.put(Attributes.SOURCE_ID.name(), this.sourceId.toString());
    }

    if (this.transformId != null) {
      attributesJson.put(Attributes.TRANSFORM_ID.name(), this.transformId.toString());
    }

    if (this.flowType != null) {
      attributesJson.put(Attributes.FLOW_TYPE.name(), this.flowType.name());
    }

    if (this.flowOwnerId != null) {
      attributesJson.put(Attributes.FLOW_OWNER_ID.name(), this.flowOwnerId.toString());
    }

    return attributesJson;
  }
}
