# base image for kafka connectors
FROM confluentinc/cp-kafka-connect-base:7.9.0
MAINTAINER Avinash "<EMAIL>"

USER root
RUN yum update -y

RUN yum -y install wget curl bind-utils iputils nc net-tools nmap openssl \
  lsof socat procps vim nano

RUN yum -y install https://dl.fedoraproject.org/pub/epel/epel-release-latest-8.noarch.rpm
RUN yum -y install htop jq

RUN chmod 666 /etc/kafka/connect-log4j.properties

ENV LOG_DIR="/var/log/kafka"
ENV KAFKA_LOG_DIRS="/var/lib/kafka"

ENV COMPONENT=kafka-connect

ENV KAFKA_JMX_PORT="2000"

ENV NEXLA_LOG_DIR="/var/log/nexla"
RUN mkdir -p $NEXLA_LOG_DIR && chmod -R a+rw $LOG_DIR && chmod -R a+rw $NEXLA_LOG_DIR
VOLUME ${NEXLA_LOG_DIR}

USER appuser

EXPOSE 8083 2000
