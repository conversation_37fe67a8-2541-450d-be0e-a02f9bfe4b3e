FROM confluentinc/cp-kafka-connect-base:6.2.0 as builder

USER root

RUN yum -y update
RUN yum -y install wget libstdc++ autoconf automake libtool autoconf-archive pkg-config gcc gcc-c++ make libjpeg-devel libpng-devel libtiff-devel zlib-devel

RUN wget https://github.com/DanBloomberg/leptonica/releases/download/1.84.1/leptonica-1.84.1.tar.gz
RUN tar -xvzf leptonica-1.84.1.tar.gz
RUN cd leptonica-1.84.1 && ./configure && make -j && make install && cd ..
RUN rm leptonica-1.84.1.tar.gz

RUN wget https://github.com/tesseract-ocr/tesseract/archive/refs/tags/5.3.4.tar.gz
RUN tar -xvzf 5.3.4.tar.gz
RUN cd tesseract-5.3.4 && ./autogen.sh && PKG_CONFIG_PATH=/usr/local/lib/pkgconfig LIBLEPT_HEADERSDIR=/usr/local/include ./configure --with-extra-includes=/usr/local/include --with-extra-libraries=/usr/local/lib && make && make install && ldconfig && cd ..
RUN rm -f 5.3.4.tar.gz

# Fetch tessdata
RUN wget https://github.com/tesseract-ocr/tessdata/archive/refs/tags/4.1.0.tar.gz
RUN rm -rf /usr/local/share/tessdata/*
RUN tar -xvzf 4.1.0.tar.gz
RUN mv tessdata-4.1.0/* /usr/local/share/tessdata
RUN rm 4.1.0.tar.gz

RUN tesseract --version

FROM nexla/base-connector-source-agent:3.3.0-kafka-connect-7.9.0-latest
MAINTAINER Avinash "<EMAIL>"

ARG GIT_HASH
ENV GIT_HASH=$GIT_HASH
COPY docker/log4j.properties /etc/confluent/docker/log4j.properties.template
COPY docker/log4j2.properties /etc/confluent/docker/log4j2.properties
ENV KAFKA_LOG4J_OPTS "-Dlog4j.configurationFile=/etc/confluent/docker/log4j2.properties -Dgit_hash=${GIT_HASH} -Djava.util.logging.manager=org.apache.logging.log4j.jul.LogManager"

ADD target/kafka-connect-*.jar /etc/${COMPONENT}/jars/

USER root
RUN rm -f /usr/share/java/kafka/slf4j-log4j*.jar
RUN rm -f /usr/share/java/cp-base-new/slf4j-log4j*.jar
RUN chown 99:99 -R /etc/kafka-connect
USER appuser

COPY --from=builder /usr/local/share/tessdata /usr/local/share/tessdata
COPY --from=builder /usr/local/bin/tesseract /usr/local/bin/tesseract
COPY --from=builder /usr/local/lib/libtesseract.so.5 /usr/local/lib/libtesseract.so
COPY --from=builder /usr/local/lib/libleptonica.so.6 /usr/local/lib/libleptonica.so.6
COPY --from=builder /lib64/libjpeg.so.62 /lib64/libjpeg.so.62
COPY --from=builder /lib64/libtiff.so.5 /lib64/libtiff.so.5
COPY --from=builder /lib64/libpng16.so.16 /lib64/libpng16.so.16
COPY --from=builder /lib64/libgomp.so.1 /lib64/libgomp.so.1
COPY --from=builder /lib64/libjbig.so.2.1 /lib64/libjbig.so.2.1

ENV TESSDATA_PREFIX "/usr/local/share/tessdata"


COPY docker/start.sh /app/start.sh
CMD /app/start.sh
