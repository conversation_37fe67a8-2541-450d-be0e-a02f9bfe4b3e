FROM nexla/base-connector-sink-agent:3.3.0-kafka-connect-7.9.0-latest
MAINTAINER Avinash "<EMAIL>"

ARG GIT_HASH
ENV GIT_HASH=$GIT_HASH

USER root
RUN yum install -y fontconfig
RUN yum install -y net-tools
USER appuser

COPY docker/log4j.properties /etc/confluent/docker/log4j.properties.template
COPY docker/log4j2.properties /etc/confluent/docker/log4j2.properties
ENV KAFKA_LOG4J_OPTS "-Dlog4j.configurationFile=/etc/confluent/docker/log4j2.properties -Dgit_hash=${GIT_HASH} -Djava.util.logging.manager=org.apache.logging.log4j.jul.LogManager"

ADD target/kafka-connect-*.jar /etc/${COMPONENT}/jars/

USER root
RUN rm -f /usr/share/java/kafka/scala-*.jar
RUN rm -f /usr/share/java/kafka/slf4j-log4j*.jar
RUN rm -f /usr/share/java/kafka-serde-tools/scala-*.jar
RUN rm -f /usr/share/java/cp-base-new/slf4j-log4j*.jar
RUN chown 99:99 -R /etc/kafka-connect
USER appuser

COPY docker/start.sh /app/start.sh
CMD /app/start.sh
