package com.nexla.connect.box;

import com.box.sdk.BoxAPIConnection;
import com.box.sdk.BoxAPIException;
import com.box.sdk.BoxFile;
import com.box.sdk.BoxFolder;
import com.box.sdk.BoxItem;
import com.nexla.admin.client.AdminApiClient;
import com.nexla.admin.client.oauth2.NexlaTokenProvider;
import com.nexla.common.ConnectionType;
import com.nexla.common.ListingResourceType;
import com.nexla.common.NexlaBucket;
import com.nexla.common.NexlaFile;
import com.nexla.common.ResourceType;
import com.nexla.common.io.CloseableInputStream;
import com.nexla.common.logging.NexlaLogKey;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.connector.config.file.BoxAuthConfig;
import com.nexla.connector.config.file.DirScanningMode;
import com.nexla.connector.config.file.FileConnectorAuth;
import com.nexla.connector.config.file.FileSourceConnectorConfig;
import com.nexla.file.service.FileConnectorService;
import com.nexla.file.service.FileDetails;
import com.nexla.file.service.FileWalk.LevelFile;
import com.nexla.listing.client.ListingClient;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.config.AbstractConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;

import static com.box.sdk.BoxFile.Permission.CAN_DOWNLOAD;
import static com.box.sdk.BoxFolder.getRootFolder;
import static com.nexla.common.ListingResourceType.FOLDER;
import static com.nexla.common.StreamUtils.isInstance;
import static com.nexla.connector.ConnectorService.AuthResponse.SUCCESS;
import static com.nexla.connector.ConnectorService.AuthResponse.authError;
import static com.nexla.connector.config.file.DirScanningMode.BOTH;
import static com.nexla.file.service.FileWalk.walkFileTreeDfs;
import static edu.emory.mathcs.backport.java.util.Arrays.asList;
import static java.lang.String.join;
import static java.util.Arrays.stream;
import static java.util.Optional.empty;
import static java.util.Optional.of;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.trimToNull;

public class BoxConnectorService extends FileConnectorService<BoxAuthConfig> {

	private Logger logger = LoggerFactory.getLogger(BoxConnectorService.class);

	private static final String FIELD_PERMISSIONS = "permissions";

	private static final String ALL_FILES = "All Files";

	private final NexlaTokenProvider tokenProvider;

	public BoxConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey) {
		super(adminApiClient, listingClient, credentialsDecryptKey);
		this.tokenProvider = NexlaTokenProvider.sameToken();
	}

	public BoxConnectorService(AdminApiClient adminApiClient, ListingClient listingClient, String credentialsDecryptKey, NexlaTokenProvider tokenProvider) {
		super(adminApiClient, listingClient, credentialsDecryptKey);
		this.tokenProvider = tokenProvider;
	}

	private static Optional<BoxItem.Info> findChildItem(BoxFolder folder, String dir) {
		return StreamEx.of(children(folder).iterator()).filter(a -> a.getName().equalsIgnoreCase(dir)).findFirst();
	}

	private static Iterable<BoxItem.Info> children(BoxFolder folder) {
		return folder.getChildren(BoxItem.ALL_FIELDS);
	}

	private static RuntimeException pathShouldStartWithAllFiles() {
		return new RuntimeException("Cannot create folder: path should start with '/All Files/'");
	}

	private static RuntimeException emptyPathException() {
		return new RuntimeException("Cannot create destination: empty path is passed");
	}

	private BoxAPIConnection getBoxClient(
		BoxAuthConfig authConfig
	) {
		BoxAPIConnection api = new BoxAPIConnection(tokenProvider.getToken(authConfig).getVendorAccessToken());
		api.setAutoRefresh(false);
		return api;
	}

	@Override
	public void initLogger(ResourceType resourceType, Integer resourceId, Optional<Integer> taskId) {
		this.logger = new NexlaLogger(logger, new NexlaLogKey(resourceType, resourceId, taskId));
	}

	@Override
	public AuthResponse authenticate(BoxAuthConfig authConfig) {
		try {
			getRootFolder(getBoxClient(authConfig)).getInfo();
			return SUCCESS;
		} catch (Throwable e) {
			logger.error("", e);
			return authError(e);
		}
	}

	@Override
	public StreamEx<NexlaFile> listBucketContents(AbstractConfig c) {

		FileSourceConnectorConfig config = (FileSourceConnectorConfig) c;

		List<String> folderPath = new ArrayList<>(asList(config.path.split("/")));
		List<String> startFolderPath = setRootIfRequired(folderPath);

		Optional<BoxFolder> startFolder = isInstance(locateItem(startFolderPath, getBoxClient(config)), BoxFolder.class);

		if (!startFolder.isPresent()) {
			throw new RuntimeException("Folder does not exist: " + startFolderPath);
		} else {
			return getFiles(startFolder.get(), config.dirScanningMode, config.depth);
		}
	}

	private List<String> setRootIfRequired(List<String> folderPaths) {
		if (folderPaths.isEmpty() || !folderPaths.get(0).equals(ALL_FILES)) {
			folderPaths.add(0, ALL_FILES);
		}
		return folderPaths;
	}

	private Optional<BoxItem> locateItem(List<String> pathCollection, BoxAPIConnection api) {
		Iterator<String> it = pathCollection.iterator();
		if (!it.hasNext()) {
			return empty();
		}
		BoxItem currItem = getRootFolder(api);
		if (!it.next().equalsIgnoreCase(currItem.getInfo().getName())) {
			return empty();
		}
		while (it.hasNext()) {
			String nextPathItem = it.next();
			if (currItem instanceof BoxFolder) {
				BoxFolder currFolder = (BoxFolder) currItem;
				Optional<BoxItem.Info> nextBoxItem = findChildItem(currFolder, nextPathItem);
				if (!nextBoxItem.isPresent()) {
					return empty();
				} else {
					currItem = (BoxItem) nextBoxItem.get().getResource();
				}
			}
		}
		return of(currItem);
	}

	@Override
	public boolean doesFileExistsInternal(FileConnectorAuth config, String key) {
		BoxAuthConfig boxAuthConfig = (BoxAuthConfig) config.getAuthConfig();
		Optional<BoxItem> boxItem = locateItem(path(key), getBoxClient(boxAuthConfig));
		return isInstance(boxItem, BoxFile.class)
			.map(file -> file.getInfo(FIELD_PERMISSIONS).getPermissions().contains(CAN_DOWNLOAD))
			.orElse(false);
	}

	private BoxAPIConnection getBoxClient(FileSourceConnectorConfig config) {
		BoxAuthConfig boxAuthConfig = (BoxAuthConfig) config.getAuthConfig();
		return getBoxClient(boxAuthConfig);
	}

	@Override
	public StreamEx<NexlaBucket> listBuckets(AbstractConfig config) {
		return StreamEx.empty();
	}

	@SneakyThrows
	@Override
	public InputStream readInputStreamInternal(FileConnectorAuth config, String pathToFile) {
		FileSourceConnectorConfig connectorConfig = (FileSourceConnectorConfig) config;
		BoxAuthConfig boxAuthConfig = (BoxAuthConfig) connectorConfig.getAuthConfig();

		List<String> pathCollection = setRootIfRequired(path(pathToFile));

		Optional<BoxFile> boxFile =
			isInstance(
				locateItem(pathCollection, getBoxClient(boxAuthConfig)),
				BoxFile.class);

		if (boxFile.isPresent()) {
			BoxFile file = boxFile.get();
			File tempFile = File.createTempFile(file.getInfo().getID(), ConnectionType.BOX.name());
			try (FileOutputStream fos = new FileOutputStream(tempFile)) {
				if (connectorConfig.downloadLimit.isPresent()) {
					file.downloadRange(fos, 0, connectorConfig.downloadLimit.get());
				} else {
					file.download(fos);
				}
			} catch (BoxAPIException e) {
				if (e.getResponse().contains("access_denied_insufficient_permissions")) {
					throw new FileNotFoundException("Access denied: " + pathToFile);
				} else {
					throw e;
				}
			}
			return new CloseableInputStream(new FileInputStream(tempFile)).onClose(tempFile::delete);
		} else {
			throw new FileNotFoundException("File is not found: " + pathToFile);
		}
	}

	private List<String> path(String pathToFile) {
		return stream(pathToFile.split("/")).filter(StringUtils::isNotBlank).collect(toList());
	}

	@Override
	public boolean checkWriteAccess(AbstractConfig c) {
		FileConnectorAuth config = (FileConnectorAuth) c;
		BoxAuthConfig boxAuthConfig = (BoxAuthConfig) config.getAuthConfig();
		BoxAPIConnection api = getBoxClient(boxAuthConfig);
		String pathToFile = "all files/242234/check_write_access_" + UUID.randomUUID() + ".txt";
		try {
			writeInternal(config, pathToFile, new ByteArrayInputStream("test".getBytes()));
			return true;
		} catch (Exception e) {
			logger.error("Failed to write a temp file", e);
			return false;
		} finally {
			try {
				locateItem(path(pathToFile), api).ifPresent(file -> ((BoxFile) file).delete());
			} catch (Exception e) {
				logger.error("", e);
			}
		}
	}

	@Override
	@SneakyThrows
	public FileDetails writeInternal(FileConnectorAuth config, String key, File file) {
		try (FileInputStream inputStream = new FileInputStream(file)) {
			return writeInternal(config, key, inputStream);
		}
	}

	@Override
	public FileDetails writeInternal(FileConnectorAuth config, String pathToFile, InputStream inputStream) {
		LinkedList<String> pathCollection = new LinkedList<>(path(pathToFile));
		String fileName = pathCollection.getLast();
		pathCollection.removeLast();
		BoxAuthConfig boxAuthConfig = (BoxAuthConfig) config.getAuthConfig();
		BoxFolder parentFolder = createDestination(pathCollection, getBoxClient(boxAuthConfig));

		parentFolder.uploadFile(inputStream, fileName);
		return new FileDetails(pathToFile, empty(), empty());
	}

	@Override
	public StreamEx<NexlaFile> listTopLevelBuckets(AbstractConfig configTemp) {
		FileSourceConnectorConfig config = (FileSourceConnectorConfig) configTemp;
		config.dirScanningMode = BOTH;
		return listBucketContents(config);
	}

	private StreamEx<NexlaFile> getFiles(BoxFolder folder, DirScanningMode dirScanningMode, Integer maxDepth) {
		return walkFileTreeDfs(
			dirScanningMode,
			() -> StreamEx
				.of(children(folder).iterator())
				.filter(BoxConnectorService::ifFileOrFolder)
				.map(f -> new LevelFile<>(1, f, null, isFolder(f))),
			currFile -> {
				if (currFile.level == maxDepth || !isFolder(currFile.file)) {
					return StreamEx.empty();
				} else {
					BoxFolder currFolder = (BoxFolder) currFile.file.getResource();
					return StreamEx
						.of(children(currFolder).iterator())
						.filter(BoxConnectorService::ifFileOrFolder)
						.map(f -> new LevelFile<>(currFile.level + 1, f, null, isFolder(f)));
				}
			})
			.map(f -> toNexlaFile(f.file));
	}

	private NexlaFile toNexlaFile(BoxItem.Info info) {
		ListingResourceType resourceType = isFolder(info) ? FOLDER : ListingResourceType.FILE;
		String parentPath = trimToNull(info.getPathCollection().stream().map(BoxItem.Info::getName).collect(joining("/")));
		String fullPath = (parentPath != null ? parentPath + "/" : "") + info.getName();
		Long modifiedAt = info.getModifiedAt() != null ? info.getModifiedAt().getTime() : null;
		Long createdAt = info.getCreatedAt() != null ? info.getCreatedAt().getTime() : null;
		String sha1 = isFile(info) ? ((BoxFile.Info) info).getSha1() : null;
		return new NexlaFile(fullPath, info.getSize(), parentPath, sha1, createdAt, modifiedAt, resourceType);
	}

	private static boolean isFile(BoxItem.Info info) {
		return info instanceof BoxFile.Info;
	}

	private static boolean isFolder(BoxItem.Info info) {
		return info instanceof BoxFolder.Info;
	}

	private static boolean ifFileOrFolder(BoxItem.Info info) {
		return isFile(info) || isFolder(info);
	}

	@Override
	public void createDestination(AbstractConfig c, Optional<Integer> sourceId) {
		FileConnectorAuth config = (FileConnectorAuth) c;
		List<String> path = path(config.getPath());
		BoxAuthConfig boxAuthConfig = (BoxAuthConfig) config.getAuthConfig();
		createDestination(path, getBoxClient(boxAuthConfig));
	}

	private BoxFolder createDestination(List<String> path, BoxAPIConnection boxClient) {
		Iterator<String> it = path.iterator();
		if (!it.hasNext()) {
			throw emptyPathException();
		}
		BoxFolder rootFolder = getRootFolder(boxClient);
		if (!it.next().equalsIgnoreCase(rootFolder.getInfo().getName())) {
			throw pathShouldStartWithAllFiles();
		}
		BoxFolder currItem = rootFolder;
		while (it.hasNext()) {
			String nextDir = it.next();
			Optional<BoxItem.Info> childItem = findChildItem(currItem, nextDir);
			if (childItem.isPresent()) {
				if (!(childItem.get().getResource() instanceof BoxFolder)) {
					throw new RuntimeException(
						"Cannot create directory=" + join("/", path) + " as file with the same name already exists");
				} else {
					currItem = (BoxFolder) childItem.get().getResource();
				}
			} else {
				currItem = currItem.createFolder(nextDir).getResource();
			}
		}
		return currItem.getInfo().getResource();
	}
}
