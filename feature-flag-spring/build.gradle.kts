import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import java.io.BufferedReader
import java.net.URI

plugins {
    id("java-library")
    kotlin("jvm")
    kotlin("plugin.spring")
    id("maven-publish")
    id("org.jlleitschuh.gradle.ktlint")
    id("com.adarshr.test-logger")
}

fun resolveVersion(version: String): String = System.getenv("SNAPSHOT")?.ifBlank { version } ?: version

version = resolveVersion("1.0.1")
group = "com.nexla.commons"

val codeArtifactToken: String = if (System.getenv("CODEARTIFACT_AUTH_TOKEN") != null) {
    System.getenv("CODEARTIFACT_AUTH_TOKEN")
} else {
    ProcessBuilder(
        "aws",
        "codeartifact",
        "get-authorization-token",
        "--domain",
        "nexla",
        "--domain-owner",
        "433433586750",
        "--query",
        "authorizationToken",
        "--region",
        "us-east-2",
        "--profile",
        "nexla-saml",
        "--output",
        "text",
    )
        .start()
        .inputStream
        .bufferedReader()
        .use(BufferedReader::readText)
}

val codeArtifactRepository: String by project

val springBootVersion: String by project
val kotlinLoggingVersion: String by project
val junitVersion: String by project
val mockkVersion: String by project
val springMockkVersion: String by project

repositories {
    mavenCentral()
    maven(url = "https://jitpack.io")
    maven {
        url = URI(codeArtifactRepository)
        credentials {
            username = "AWS"
            password = codeArtifactToken
        }
    }
    mavenLocal()
}

dependencies {
    compileOnly(platform("org.springframework.boot:spring-boot-dependencies:$springBootVersion"))
    compileOnly("org.springframework.boot:spring-boot-starter")
    compileOnly("com.fasterxml.jackson.module:jackson-module-kotlin")

    // GrowthBook
    implementation("com.github.growthbook:growthbook-sdk-java:0.9.91")

    // Logging
    implementation("io.github.oshai:kotlin-logging-jvm:$kotlinLoggingVersion")

    // Tests
    testImplementation(platform("org.springframework.boot:spring-boot-dependencies:$springBootVersion"))
    testImplementation(platform("org.junit:junit-bom:$junitVersion"))
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testImplementation("org.junit.jupiter:junit-jupiter-params")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testImplementation("com.ninja-squad:springmockk:$springMockkVersion")
    testImplementation("io.mockk:mockk:$mockkVersion")
}

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
    withSourcesJar()
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
        jvmTarget = "17"
    }
}

tasks.withType<Test> {
    val arguments = listOf(
        "--add-opens",
        "java.base/java.lang=ALL-UNNAMED",
    )
    jvmArgs(arguments)
    useJUnitPlatform()
}

// Publishing
publishing {
    publications {
        create<MavenPublication>("main") {
            from(components["java"])
        }
    }
    repositories {
        maven {
            name = "codeArtifactRepository"
            url = uri(codeArtifactRepository)
            credentials {
                username = "AWS"
                password = codeArtifactToken
            }
        }
    }
}

configure<org.jlleitschuh.gradle.ktlint.KtlintExtension> {
    version.set("1.1.1")
    additionalEditorconfig.set(
        mapOf(
            "max_line_length" to "off",
            "ktlint_standard_value-parameter-comment" to "disabled",
            "ktlint_standard_value-argument-comment" to "disabled",
            "ktlint_standard_multiline-expression-wrapping" to "disabled",
            "ktlint_standard_string-template-indent" to "disabled",
            "ktlint_standard_parameter-list-wrapping" to "disabled",
            "ktlint_standard_function-signature" to "disabled",
            "ktlint_standard_if-else-wrapping" to "disabled",
            "ktlint_standard_statement-wrapping" to "disabled",
            "ktlint_standard_try-catch-finally-spacing" to "disabled",
        ),
    )
    reporters {
        reporter(org.jlleitschuh.gradle.ktlint.reporter.ReporterType.PLAIN)
        reporter(org.jlleitschuh.gradle.ktlint.reporter.ReporterType.CHECKSTYLE)
    }
}
