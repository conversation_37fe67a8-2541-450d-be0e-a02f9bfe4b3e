logging.file=/tmp/admin-api-client.log

entitymanager.packagesToScan=com.nexla

#spring.jpa.database-platform=org.hibernate.dialect.MySQL5Dialect
#spring.jpa.show-sql=true
#spring.jpa.hibernate.ddl-auto=create
#spring.datasource.driverClassName=com.mysql.jdbc.Driver
#spring.datasource.url=*****************************************************
#spring.datasource.username=root
#spring.datasource.password=

api.credentials.server=http://localhost:3000
api.datasource.endpoint=/data_sources
api.publisher.endpoint=/data_pubs
api.datamap.endpoint=/data_maps
#api.marathon.endpoint=http://localhost:8080
#api.marathon.endpoint=http://mesos-pub-elasticl-1aonxfdxz5et-500346342.us-east-1.elb.amazonaws.com/service/marathon/
#docker.credentials.location=https://s3.amazonaws.com/mesos-config3/docker.tar.gz
