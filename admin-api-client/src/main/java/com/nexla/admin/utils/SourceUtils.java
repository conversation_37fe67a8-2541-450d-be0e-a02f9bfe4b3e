package com.nexla.admin.utils;

import static com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet;

import com.nexla.admin.client.*;
import com.nexla.common.NexlaConstants;
import com.nexla.common.StreamUtils;
import com.nexla.connector.config.FlowType;
import com.nexla.telemetry.utils.ExecutionMetricSet;
import java.util.Map;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SourceUtils {
  private static final Logger logger = LoggerFactory.getLogger(SourceUtils.class);

  public static boolean isSparkFlow(DataSource source) {
    Map<String, Object> sourceCfg = source.getSourceConfig();
    if (sourceCfg != null) {
      Object pipelineType = sourceCfg.get(NexlaConstants.PIPELINE_TYPE);
      if (pipelineType != null) {
        FlowType type = FlowType.fromString(String.valueOf(pipelineType));
        return type.equals(FlowType.SPARK);
      }
    }

    return false;
  }

  // from this version onwards, Listing is able to detect Apache Hive partitions on its own.
  // keeping this for the code to be still compile-able
  @Deprecated
  public static boolean isHivePartitioned(DataSource source) {
    return isSparkFlow(source);
  }

  public static Map<String, Object> toResourceDto(DataSource dataSource) {
    Set<String> skipFields = Set.of();
    return StreamUtils.toResourceDto(dataSource, skipFields);
  }

  public static Map<String, Object> toResourceDto(DataSink dataSink) {
    Set<String> skipFields = Set.of();
    return StreamUtils.toResourceDto(dataSink, skipFields);
  }

  public static Map<String, Object> toResourceDto(DataSet dataSet) {
    Set<String> skipFields = Set.of();
    return StreamUtils.toResourceDto(dataSet, skipFields);
  }

  public static Map<String, Object> toResourceDto(DataCredentials dataCredentials) {
    Set<String> skipFields = Set.of();
    return StreamUtils.toResourceDto(dataCredentials, skipFields);
  }

  private static final ExecutionMetricSet dtoToResourceCheckedMetrics =
      metricSet(SourceUtils.class, "dtoToResourceChecked");

  public static <T extends WithClassVersion> T dtoToResourceChecked(
      Map<String, Object> r, Class<T> claz) {
    final T result = dtoToResource(r, claz);
    if (result == null) {
      return null;
    }
    final int objectVersion = result.getObjectVersion();
    dtoToResourceCheckedMetrics.incCounter(
        String.format("version.%s%d", claz.getSimpleName(), objectVersion));
    if (objectVersion != result.getClassVersion()) {
      dtoToResourceCheckedMetrics.incCounter(String.format("filtered.%s", claz.getSimpleName()));
      return null;
    }
    return result;
  }

  private static final ExecutionMetricSet dtoToResourceMetrics =
      metricSet(SourceUtils.class, "dtoToResource");

  public static <T> T dtoToResource(Map<String, Object> r, Class<T> claz) {
    String json = null;
    try (var ignored = dtoToResourceMetrics.time()) {
      json = StreamUtils.jsonUtil().toJsonString(r);
      return StreamUtils.jsonUtil().stringToType(json, claz);
    } catch (Exception e) {
      logger.error("Failed to map dto to resource: {}", json != null ? json : r, e);
      dtoToResourceMetrics.incError();
      return null;
    }
  }
}
