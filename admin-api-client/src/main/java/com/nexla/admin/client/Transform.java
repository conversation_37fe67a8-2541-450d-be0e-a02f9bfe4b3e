package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import lombok.Data;

@Data
public class Transform {
  private final Integer id;
  private final String name;

  private final Boolean reusable;

  @JsonProperty("public")
  private final Boolean publicFlag;

  private final Owner owner;
  private final Org org;
  private final String description;

  @JsonProperty("code_type")
  private final String codeType;

  private final ArrayList<TransformCode> code;

  @JsonProperty("output_type")
  private final String outputType;
}
