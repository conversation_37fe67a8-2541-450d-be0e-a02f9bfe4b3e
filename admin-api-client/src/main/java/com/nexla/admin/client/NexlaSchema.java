package com.nexla.admin.client;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class NexlaSchema implements Serializable {

  @JsonProperty("$schema")
  private String schema;

  private String type;

  private Object properties;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private List<String> required;

  @JsonProperty("$schema-id")
  private Integer schemaId;
}
