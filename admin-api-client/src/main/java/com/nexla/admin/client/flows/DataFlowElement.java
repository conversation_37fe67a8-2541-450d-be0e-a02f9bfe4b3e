package com.nexla.admin.client.flows;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Optional;
import lombok.Data;

@Data
public class DataFlowElement {

  private final Integer id;

  @JsonProperty("parent_data_set_id")
  private final Integer parendDataSetId;

  private final List<DataFlowElement> children;

  @JsonProperty("data_source")
  private final Optional<DataFlowElementSource> source;

  @JsonProperty("data_sinks")
  private final List<DataFlowSink> dataSinks;
}
