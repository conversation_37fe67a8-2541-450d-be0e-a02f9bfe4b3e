package com.nexla.listing.client;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Map;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdaptiveFlowTask {

  public enum TaskStatus {
    NEW,
    IN_PROGRESS,
    STOPPED,
    DONE;

    @JsonCreator
    public static TaskStatus fromString(String string) {
      return TaskStatus.valueOf(string.toUpperCase());
    }
  }

  private Long id;
  private Map<String, Object> parameters;
  private Integer sourceId;
  private Integer flowId;
  private Integer orgId;
  private TaskStatus status;
  private Long createdAt;
  private Long updatedAt;
  private String message;

  public AdaptiveFlowTask(
      Long id,
      Map<String, Object> parameters,
      Integer sourceId,
      Integer flowId,
      Integer orgId,
      TaskStatus status,
      Long createdAt,
      Long updatedAt,
      String message) {
    this.id = id;
    this.parameters = parameters;
    this.sourceId = sourceId;
    this.flowId = flowId;
    this.orgId = orgId;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.message = message;
  }
}
