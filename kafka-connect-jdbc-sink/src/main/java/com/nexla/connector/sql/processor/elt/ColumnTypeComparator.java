package com.nexla.connector.sql.processor.elt;

import lombok.AllArgsConstructor;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

public class ColumnTypeComparator {

  /**
   * Compares two database column types to determine if they are equivalent.
   * This method normalizes numeric types by ensuring that `NUMBER(x)` is treated
   * the same as `NUMBER(x, 0)`, and also ignores case differences in type names.
   *
   * @param dbType1 The first column type (e.g., "NUMBER(20,0)").
   * @param dbType2 The second column type (e.g., "NUMBER(20)").
   * @return {@code true} if the types are equivalent, {@code false} otherwise.
   */
  public static boolean areColumnTypesEqual(String dbType1, String dbType2) {
    ParsedType parsedType1 = parseColumnType(dbType1);
    ParsedType parsedType2 = parseColumnType(dbType2);

    return parsedType1.equals(parsedType2);
  }

  private static ParsedType parseColumnType(String columnType) {
    if (columnType == null) {
      return new ParsedType("", null, null);
    }

    Pattern pattern = Pattern.compile("(\\w+)\\((\\d+)(?:,\\s*(\\d+|NULL|null))?\\)");
    Matcher matcher = pattern.matcher(columnType);

    if (matcher.matches()) {
      String type = matcher.group(1);
      Integer precision = Integer.valueOf(matcher.group(2));
      String rawScale = matcher.group(3);
      Integer scale = nonNull(rawScale) && !"null".equalsIgnoreCase(rawScale) ? Integer.parseInt(rawScale) : 0; // Default scale to 0

      return new ParsedType(type, precision, scale);
    }
    return new ParsedType(columnType, null, null); // For cases like "TEXT" or "BOOLEAN"
  }

  @AllArgsConstructor
  private static class ParsedType {
    private final String type;
    private final Integer precision;
    private final Integer scale;

    @Override
    public boolean equals(Object obj) {
      if (this == obj) {
        return true;
      }
      if (obj == null || getClass() != obj.getClass()) {
        return false;
      }

      ParsedType that = (ParsedType) obj;
      return type.equalsIgnoreCase(that.type) &&
          (isNull(precision) || isNull(that.precision) || precision.equals(that.precision)) &&
          (isNull(scale) || isNull(that.scale) || scale.equals(that.scale));
    }
  }
}
