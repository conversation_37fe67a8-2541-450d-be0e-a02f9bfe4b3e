package com.nexla.connector.sql.sink;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Lists;
import com.nexla.common.ConnectionType;
import com.nexla.common.NexlaMessage;
import com.nexla.common.metrics.RecordMetric;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogSeverity;
import com.nexla.common.notify.monitoring.NexlaMonitoringLogType;
import com.nexla.common.sink.TopicPartition;
import com.nexla.connect.common.BaseSinkTask;
import com.nexla.connect.common.PostponedFlush;
import com.nexla.connect.common.ReadyToFlush;
import com.nexla.connect.common.connector.telemetry.ConnectorTelemetryReporter;
import com.nexla.connect.common.postponedFlush.KafkaProgressTracker;
import com.nexla.connect.common.postponedFlush.PipelineProgressTracker;
import com.nexla.connector.NexlaMessageContext;
import com.nexla.connector.config.jdbc.JdbcAuthConfig;
import com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig;
import com.nexla.connector.config.jdbc.WarehouseCopyFileFormat;
import com.nexla.connector.config.rest.BaseAuthConfig;
import com.nexla.connector.config.ssh.HostPort;
import com.nexla.connector.sql.processor.FlushProcessor;
import com.nexla.connector.sql.processor.TableProcessor;
import com.nexla.probe.sql.SqlConnectionException;
import com.nexla.probe.sql.SqlConnectorService;
import connect.data.Field;
import connect.data.Schema;
import connect.jdbc.sink.dialect.DbDialect;
import connect.jdbc.sink.dialect.DialectRegistry;
import connect.jdbc.sink.dialect.copy.CopyOperationLocalBuffer;
import connect.jdbc.sink.dialect.copy.FlushResult;
import connect.jdbc.sink.dialect.copy.SinkCopyOperation;
import connect.jdbc.sink.dialect.copy.exception.OracleAutonomousWholeBatchFailedException;
import connect.jdbc.sink.warehouse.DataWarehouseSink;
import connect.jdbc.util.JdbcUtils;
import io.debezium.jdbc.JdbcConnectionException;
import lombok.Getter;
import lombok.SneakyThrows;
import one.util.streamex.StreamEx;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.ibatis.jdbc.ScriptRunner;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.connect.errors.ConnectException;
import org.apache.kafka.connect.errors.RetriableException;

import java.io.Reader;
import java.io.StringReader;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.nexla.common.Resource.sink;
import static com.nexla.common.ResourceType.SINK;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.INSERT;
import static com.nexla.connector.config.jdbc.JdbcSinkConnectorConfig.InsertMode.UPSERT;
import static com.nexla.probe.sql.SchemaUtils.selectSchema;
import static com.nexla.probe.sql.SqlConnectorService.shouldRethrow;
import static connect.jdbc.sink.dialect.DialectFeature.*;
import static java.lang.Math.max;
import static java.util.Optional.of;
import static java.util.stream.Collectors.toList;

public class JdbcSinkTask extends BaseSinkTask<JdbcSinkConnectorConfig> {

	protected static final Set<ConnectionType> TYPES_ENABLED_FOR_NEW_FLOW = Set.of(ConnectionType.REDSHIFT, ConnectionType.SNOWFLAKE, ConnectionType.ORACLE_AUTONOMOUS);

	protected volatile SqlConnectorService probeService;

	protected DbDialect dbDialect;
	private Supplier<Schema> schema;
	protected SinkCopyOperation copyOperation;

	protected java.util.function.Supplier<Connection> connProvider;

	private DataCollection batch = new InMemoryCollection();

	private final Queue<DataCollection> batchQueue = new LinkedList<>();
	private final Queue<Future<Map<TopicPartition, Long>>> flushQueue = new LinkedList<>();

	protected volatile ExecutorService executor;
	@Getter
	protected boolean copyMode;

	protected DataWarehouseSink dataWarehouseSink;

	protected final AtomicLong lastProcessedMessageTs = new AtomicLong(0);

	protected final Set<Long> runIdsToHeartbeat = ConcurrentHashMap.newKeySet();
	protected final Map<Long, RecordMetric> metricsByRunId = new ConcurrentHashMap<>();

	protected TableProcessor tableProcessor;

	protected FlushProcessor flusher;

	protected Optional<PipelineProgressTracker> pipelineProgressTracker = Optional.empty();
	private long startTime = System.currentTimeMillis();

	@Override
	public void doStart() {
		logger.info("[SINK-{}] Starting JDBC Sink Task with config: url={}, table={}, schema={}, insertMode={}, batchSize={}, parallelism={}, copyMode={}",
			config.sinkId, config.authConfig.url, config.table, config.authConfig.schemaName,
			config.insertMode, config.batchSize, config.parallelism, config.copyAllowed);

		// Log additional Databricks connection details for debugging user agent
		if (config.authConfig.dbType.toString().equals("DATABRICKS")) {
			logger.info("[SINK-{}] Databricks connection details: host={}, port={}, warehouseName={}, databaseName={}",
				config.sinkId, config.authConfig.host, config.authConfig.port, 
				config.authConfig.warehouseName, config.authConfig.databaseName);
			
			// Add UserAgentEntry to Databricks URL if not already present
			if (config.authConfig.url != null && !config.authConfig.url.contains("UserAgentEntry=")) {
				String originalUrl = config.authConfig.url;
				String separator = originalUrl.endsWith(";") ? "" : ";";
				config.authConfig.url = originalUrl + separator + "UserAgentEntry=nexla-connector";
				logger.info("[SINK-{}] Updated Databricks URL to include UserAgentEntry: {}", config.sinkId, config.authConfig.url);
			}
		}

		this.sinkTelemetryReporter =
				Optional.of(new ConnectorTelemetryReporter(config.sinkId, config.authConfig.dbType.name(),
					SINK, isDedicatedNode));

		this.probeService = new SqlConnectorService(nexlaCredentialsStore, adminApiClient);
		this.probeService.initLogger(SINK, config.sinkId, taskId);

		this.dbDialect = DialectRegistry.getInstance().fromConnectionString(config.authConfig);
		logger.debug("[SINK-{}] Using database dialect: {}", config.sinkId, dbDialect.getClass().getSimpleName());

		this.connProvider = () -> probeService.getConnection(config.authConfig);

		this.schema = Suppliers.memoize(() -> selectSchema(connProvider, dbDialect, config));
		Schema currentSchema = schema.get();
		if (currentSchema != null) {
			logger.info("[SINK-{}] Schema initialized with {} fields", config.sinkId, currentSchema.fields().size());
		} else {
			logger.info("[SINK-{}] Schema is null (database dialect doesn't support schema)", config.sinkId);
		}

		this.tableProcessor = new TableProcessor(dbDialect, connProvider, probeService, config.sinkId);
		this.copyMode = config.copyAllowed &&
				((config.insertMode == UPSERT && dbDialect.getFeatures().contains(COPY_UPSERT)) ||
						(config.insertMode == INSERT && dbDialect.getFeatures().contains(COPY_INSERT)));
		logger.info("[SINK-{}] Copy mode enabled: {}, features: {}", config.sinkId, copyMode, dbDialect.getFeatures());

		if (copyMode && !config.cdcEnabled) {
			WarehouseCopyFileFormat fileFormat = getFileFormat();
			logger.info("[SINK-{}] Using file format: {}", config.sinkId, fileFormat);

			if (!shouldUseNewCopyFlow()) {
				this.copyOperation = dbDialect
						.newSinkCopyOperation(config)
						.init(currentSchema, config, adminApiClient, fileFormat, logger);
			} else {
				this.dataWarehouseSink = dbDialect.newDataWarehouseSink(config, fileFormat, schema.get(), dbDialect, logger);
				this.dataWarehouseSink.onConnectorStart(connProvider);

				this.flusher = new FlushProcessor(tableProcessor, config.sinkId, getExceptionHandler(), getMetricHandler(), lastProcessedMessageTs);
				PipelineProgressTracker ppt = pipelineProgressTracker.orElseGet(() -> new KafkaProgressTracker(getKafkaLagCalculator(), adminApiClient, ctrlClient, logger));
				this.postponedFlush = flusher.getPostponedFlush(this, ppt, dataWarehouseSink, lastProcessedMessageTs, logger);
			}
		}

		if (config.parallelism > 1) {
			this.executor = Executors.newWorkStealingPool(config.parallelism);
		}

		open(context.assignment());
		logger.info("[SINK-{}] JDBC Sink Task started successfully", config.sinkId);
	}

	@Override
	public void open(Collection<org.apache.kafka.common.TopicPartition> partitions) {
		super.open(partitions);
		if (postponedFlush != null) {
			postponedFlush.resetFirstFlushTsThreshold();
		}
	}

	public void overridePipelineProgressTracker(PipelineProgressTracker pipelineProgressTracker) {
		this.pipelineProgressTracker = Optional.ofNullable(pipelineProgressTracker);
	}

	protected WarehouseCopyFileFormat getFileFormat() {
		WarehouseCopyFileFormat fileFormat = dbDialect.defaultSinkFileFormat().get();
		if (config.intermediateFileFormat != null) {
			Optional<WarehouseCopyFileFormat> optConfigFileFormat = StreamEx.of(WarehouseCopyFileFormat.values())
					.filter(f -> f.name().equals(config.intermediateFileFormat.toUpperCase()))
					.filter(f -> dbDialect.sinkFileFormats().contains(f))
					.findFirst();

			if (optConfigFileFormat.isPresent()) {
				fileFormat = optConfigFileFormat.get();
			} else {
				logger.warn("[SINK-{}] Provided file format is not valid/supported: {}, default format will be used.", config.sinkId, config.intermediateFileFormat);
			}
		}
		return fileFormat;
	}

	@Override
	protected ConfigDef configDef() {
		return JdbcSinkConnectorConfig.configDef();
	}

	@Override
	protected JdbcSinkConnectorConfig parseConfig(Map<String, String> props) {
		return new JdbcSinkConnectorConfig(props);
	}

	protected void onRunIdChanged(Long prevRunId, Long currRunId) {
		if (config.truncOnLoad) {
			// TODO add support for stateful JDBC sinks
			this.probeService.truncateDestination(config);
			this.runIdsToHeartbeat.clear();
		} else {
			logger.debug("[SINK-{}] Run id changed from {} to {}", config.sinkId, prevRunId, currRunId);
			logger.debug("[SINK-{}] RunIdsToHeartbeat: {}", config.sinkId, runIdsToHeartbeat);
		}
	}

	@SneakyThrows
	@Override
	protected void doPut(StreamEx<NexlaMessageContext> messages, int streamSize) {
		logger.info("[SINK-{}] Processing batch of {} messages", config.sinkId, streamSize);

		if (!isParallel()) {
			Map<TopicPartition, Long> offsets = new HashMap<>();

			StreamEx<NexlaMessageContext> streamUpdatingOffsets = config.fastMode
					? messages
					: messages.peek(m -> offsets.put(m.topicPartition, m.kafkaOffset));

			processBatch(streamUpdatingOffsets, streamSize);

			offsetsSender
					.filter(x -> !copyMode)            // TODO: Dangerous condition. Consider to mange offsets in BaseSinkTask
					.ifPresent(os -> {
						os.updateSinkOffsets(config.sinkId, offsets);
						logger.info("[SINK-{}] Updated offsets: {}", config.sinkId, offsets);
					});
		} else {
			messages.forEach(batch::add);

			if (batch.size() >= config.batchSize) {
				cutBatch();
			}
			tryFlushNextBatches(false);
			commitOffsetsForFlushedBatches(false);
		}
	}

	private boolean isParallel() {
		return config.parallelism > 1;
	}

	@SneakyThrows
	private void commitOffsetsForFlushedBatches(boolean blocking) {
		while (!flushQueue.isEmpty() && (blocking || flushQueue.peek().isDone())) {
			Map<TopicPartition, Long> offsets = flushQueue.peek().get();
			try {
				offsetsSender.ifPresent(os -> {
					os.updateSinkOffsets(config.sinkId, offsets);
					logger.info("[SINK-{}] Updated offsets: {}", config.sinkId, offsets);
				});
				flushQueue.remove();
			} catch (Exception e) {
				logger.error("[SINK-{}] Failed to update offset, trying later, flushQueue size={}", config.sinkId, flushQueue.size(), e);
				break;
			}
		}
	}

	private void tryFlushNextBatches(boolean blocking) {
		while (!batchQueue.isEmpty() && (blocking || flushQueue.size() < config.parallelism)) {
			DataCollection batchToFlush = batchQueue.poll();
			flushQueue.add(executor.submit(() -> flushBatch(batchToFlush)));
		}
	}

	private void cutBatch() {
		batch.closeResources();
		batchQueue.add(batch);
		this.batch = batchQueue.size() >= config.parallelism ? new FileCollection() : new InMemoryCollection();
		logger.info("[SINK-{}] Using {}", config.sinkId, batch.getClass().getSimpleName());
	}

	private Map<TopicPartition, Long> flushBatch(DataCollection batchToFlush) {
		try (StreamEx<NexlaMessageContext> stream = batchToFlush.readAll()) {
			processBatch(stream, batchToFlush.size());
		}
		batchToFlush.deleteFile();
		return batchToFlush.lastOffsetMap();
	}

	protected void processBatch(StreamEx<NexlaMessageContext> messages, int streamSize) {
		StopWatch stopWatch = new StopWatch();
		stopWatch.start();

		logger.info("[SINK-{}] Writing {} records to table={}", config.sinkId, streamSize, config.table);
		final List<NexlaMessageContext> messageList;
		if (messages != null) {
			messageList = messages.toList();
		} else {
			logger.warn("[SINK-{}] Message contexts are empty, creating empty list to avoid further errors", config.sinkId);
			messageList = Collections.emptyList();
		}
		final Map<Long, Integer> streamSizeByRunId = new HashMap<>();
		messageList.forEach(message -> {
			Long runId = 0L;
			if (message.getOriginal() != null && message.getOriginal().getNexlaMetaData() != null && message.getOriginal().getNexlaMetaData().getRunId() != null) {
				runId = message.getOriginal().getNexlaMetaData().getRunId();
			}
			streamSizeByRunId.compute(runId, (key, value) -> value == null ? 1 : value + 1);
		});

		ExceptionHandler handler = getExceptionHandler();
		try {
			if (copyMode) {
				final List<NexlaMessageContext> dataStream = deduplicate(StreamEx.of(messageList), config).collect(toList()); // Important. Do not filter records in any other way before they are written to CopyOperation buffer, because buffer keeps messages offset and it should receive the offset of the last message in batch
				dataStream.stream()
						.filter(m -> m.getMapped() != null && m.getMapped().getNexlaMetaData() != null)
						.forEach(m -> runIdsToHeartbeat.add(m.getMapped().getNexlaMetaData().getRunId()));
				if (shouldUseNewCopyFlow()) {
					if (!messageList.isEmpty()) {
						// logic for filtering primary keys and dedup is in dataWarehouseSink, so DO NOT use dataStream variable here
						dataWarehouseSink.writeData(messageList, streamSizeByRunId);
						lastProcessedMessageTs.set(System.currentTimeMillis());
					}
				} else {
					copyOperation.writeToBuffer(dataStream, streamSizeByRunId);
				}
			} else {
				final Map<Long, RecordMetric> batchMetricsByRunId = new HashMap<>();
				final List<NexlaMessageContext> filteredMessages = config.insertMode == UPSERT
						? filterNullPrimaryKeys(messageList, config.primaryKey, batchMetricsByRunId)
						: messageList;

				// Preserve Original Msg's RunId in Mapped Messages Tags
				filteredMessages.forEach(message -> {
					if (message.getOriginal() != null && message.getOriginal().getNexlaMetaData() != null && message.getOriginal().getNexlaMetaData().getRunId() != null) {
						if (message.getMapped() != null && message.getMapped().getNexlaMetaData() != null) {
							if (message.getMapped().getNexlaMetaData().getTags() == null) {
								message.getMapped().getNexlaMetaData().setTags(new HashMap<>());
							}
							message.getMapped().getNexlaMetaData().getTags().put("originalMessageRunId", message.getOriginal().getNexlaMetaData().getRunId());
						}
					}
				});

				final StreamEx<NexlaMessageContext> dataStream = deduplicate(StreamEx.of(filteredMessages), config);
				final Map<Long, RecordMetric> executeJdbcMetric = executeJdbc(dataStream.map(x -> x.mapped).toList(), handler);
				for (Map.Entry<Long, RecordMetric> entry: executeJdbcMetric.entrySet()) {
					batchMetricsByRunId.merge(entry.getKey(), entry.getValue(), RecordMetric::combine);
				}
				// NEX-3761 number of input records for JDBC sink should always be the same size as input dataset
				// That means, take number before deduplication
				batchMetricsByRunId.forEach((runId, recordMetric) -> {
					sendQuarantineMessage(config.table, recordMetric.getQuarantineMessages());
					sendMetricsByRunId(runId, config.table, Optional.empty(), recordMetric.sentRecordsTotal.get(), recordMetric.sentBytesTotal.get(), recordMetric.errorRecords.get());
				});
			}

			if (isParallel()) {
				logger.info("[SINK-{}] RECORDS={} FLUSH TIME={}", config.sinkId, streamSize, stopWatch.toString());
			}
		} catch (RetriableException e) {
			metricsByRunId.clear();
			throw e;
		} catch (Exception e) {
			if (shouldRethrow(e)) {
				metricsByRunId.clear();
				throw new RetriableException(e);
			}
			logger.error("[SINK-{}] Error while writing records to sinkId={}, records={}", config.sinkId, config.sinkId, streamSize, e);

			Map<Long, List<NexlaMessageContext>> errorMessagesByRunId = messageList.stream()
					.collect(Collectors.groupingBy(m -> {
						if (m.getOriginal() == null || m.getOriginal().getNexlaMetaData() == null || m.getOriginal().getNexlaMetaData().getRunId() == null) {
							return 0L;
						}
						return m.getOriginal().getNexlaMetaData().getRunId();
					}));

			for (Map.Entry<Long, List<NexlaMessageContext>> entry : errorMessagesByRunId.entrySet()) {
				Long runId = entry.getKey();
				List<NexlaMessageContext> runMessages = entry.getValue();

				RecordMetric recordMetric = metricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());

				runMessages.forEach(m -> sendToQuarantine(m.mapped, e, recordMetric));
				sendQuarantineMessage(config.table, recordMetric.getQuarantineMessages());
				sendMetricsByRunId(runId, config.table, Optional.empty(), 0L, 0L, recordMetric.errorRecords.get());

				handler.handleException(e, recordMetric.errorRecords.get(), config.table);
				handler.handleMonitoring(e);
			}

			if (config.stopOnError) {
				throw new RuntimeException(e);
			}
		}
	}

	@VisibleForTesting
	List<NexlaMessageContext> filterNullPrimaryKeys(
			final List<NexlaMessageContext> messageList,
			final List<String> primaryKey,
			final Map<Long, RecordMetric> metricsByRunId) {
		return messageList
				.stream()
				.filter(message -> {
					if (TableProcessor.extractPrimaryKey(primaryKey, message.getMapped().getRawMessage()).contains(null)) {
						Long messageRunId = 0L;
						if (message.getOriginal() != null && message.getOriginal().getNexlaMetaData() != null && message.getOriginal().getNexlaMetaData().getRunId() != null) {
							messageRunId = message.getOriginal().getNexlaMetaData().getRunId();
						}
						RecordMetric recordMetric = metricsByRunId.computeIfAbsent(messageRunId, k -> new RecordMetric());
						recordMetric.quarantineMessages.add(RecordMetric.quarantineMessage(message.getMapped(), "Null value in primary key"));
						recordMetric.errorRecords.incrementAndGet();
						return false;
					}
					return true;
				})
				.collect(Collectors.toList());
	}

	protected void sendToQuarantine(NexlaMessage message, Throwable exp, RecordMetric recordMetric) {
		dataMessageProducer.sendQuarantineMessage(sink(config.sinkId), message, exp);
		recordMetric.errorRecords.incrementAndGet();
	}

	@VisibleForTesting
	public StreamEx<NexlaMessageContext> deduplicate(StreamEx<NexlaMessageContext> dataStream, JdbcSinkConnectorConfig sinkConfig) {
		if (sinkConfig.insertMode == UPSERT) {
			return dedupByPk(dataStream, sinkConfig.primaryKey);
		} else {
			return dataStream;
		}
	}

	private boolean isTimerPassed() {
		long currentTime = System.currentTimeMillis();
		long elapsedTime = currentTime - startTime;

		// Check if X minutes have passed
		if (config.flushIntervalMin.isEmpty() || elapsedTime >= config.flushIntervalMin.get() * 60 * 1000) {
			// Reset the start time
			startTime = System.currentTimeMillis();
			logger.debug("[SINK-{}] Timer Passed, let's flush sinkId={}....", config.sinkId);
			return true;
		}

		logger.debug("[SINK-{}] Too early for flushing sinkId={}....", config.sinkId);
		return false;
	}

	@Override
	public boolean doFlush(ReadyToFlush readyToFlush) {
		logger.info("[SINK-{}] Starting flush operation, readyToFlush={}", config.sinkId, readyToFlush);

		if (isParallel()) {
			if (flushQueue.isEmpty() && batch.size() > 0) {
				cutBatch();
			}
			tryFlushNextBatches(true);
			commitOffsetsForFlushedBatches(true);
		}
		if (copyMode) {
			if (shouldUseNewCopyFlow()) {
				return flusher.flush(this.dataWarehouseSink, config, this.postponedFlush, this.pipelineStatusReport, readyToFlush);
			} else {
				if (!isTimerPassed()) {
					logger.debug("[SINK-{}] Timer not passed, skipping flush", config.sinkId);
					return false;
				}
				synchronized (copyOperation) {
					try {
						List<CopyOperationLocalBuffer> buffersToFlush = copyOperation
								.getBuffers()
								.stream()
								.filter(x -> x.streamSize() > 0)
								.collect(toList());
						pipelineStatusReport.ifPresent(status -> status.reportPipelineFlushStarted(buffersToFlush.size()));
						buffersToFlush.forEach(buffer -> {
							logger.debug("[SINK-{}] Flushing buffer with {} records", config.sinkId, buffer.streamSize());
							Map<Long, RecordMetric> metricsByRunId = copyOperation.flushBufferByRunId(buffer, connProvider, config.insertMode);
							updateSinkOffsets(buffer.getOffsets());

							metricsByRunId.forEach((runId, recordMetric) -> {
								logger.debug("[SINK-{}] Run {} metrics - records: {}, bytes: {}, errors: {}",
									config.sinkId, runId,
									recordMetric.sentRecordsTotal.get(),
									recordMetric.sentBytesTotal.get(),
									recordMetric.errorRecords.get());

								sendMetricsByRunId(runId, config.table, Optional.empty(),
									recordMetric.sentRecordsTotal.get(),
									recordMetric.sentBytesTotal.get(),
									recordMetric.errorRecords.get());
								sendQuarantineMessage(config.table, recordMetric.getQuarantineMessages());
							});

							pipelineStatusReport.ifPresent(status -> status.flushProgress.addAndGet(1));
						});
						Optional.ofNullable(this.postponedFlush).ifPresent(PostponedFlush::resetFailures);
					} catch (RetriableException e) {
						logger.error("[SINK-{}] Retriable error during flush", config.sinkId, e);
						metricsByRunId.clear();
						Optional<PostponedFlush> pf = Optional.ofNullable(this.postponedFlush);
						pf.ifPresent(flush -> flush.incrementFailures(e));
						throw e;
					} catch (OracleAutonomousWholeBatchFailedException e) {
						Map<Long, RecordMetric> errorMetricsByRunId = new HashMap<>();
						int streamSize = copyOperation.onUnprocessedBuffers(message -> {
							Long messageRunId;
							if (message != null && message.getNexlaMetaData() != null && message.getNexlaMetaData().getRunId() != null) {
								messageRunId = message.getNexlaMetaData().getRunId();
							} else {
								messageRunId = this.runId;
							}
							RecordMetric recordMetric = errorMetricsByRunId.computeIfAbsent(messageRunId, k -> new RecordMetric());
							sendToQuarantine(message, e, recordMetric);
						});
						logger.error("OracleAutonomous entire batch failed while writing records to sinkId={}, records={}", config.sinkId, streamSize, e);

						errorMetricsByRunId.forEach((runId, recordMetric) -> {
							sendMetricsByRunId(runId, config.table, Optional.empty(), 0L, 0L, recordMetric.errorRecords.get());
							publishExceptionMessage(e, recordMetric.errorRecords.get(), config.table);
						});
						publishMonitoringLog("Error while writing records to destination: " + e.getMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
						if (config.stopOnError) {
							throw new RuntimeException(e);
						} else {
							reportTelemetryFromError(e);
						}
					} catch (Exception e) {
						if (shouldRethrow(e)) {
							metricsByRunId.clear();
							throw new RetriableException(e);
						}
						Map<Long, RecordMetric> errorMetricsByRunId = new HashMap<>();
						int streamSize = copyOperation.onUnprocessedBuffers(message -> {
							Long runId = message.getNexlaMetaData().getRunId();
							if (runId != null) {
								RecordMetric recordMetric = errorMetricsByRunId.computeIfAbsent(runId, k -> new RecordMetric());
								sendToQuarantine(message, e, recordMetric);
							}
						});
						logger.error("Error while writing records to sinkId={}, records={}", config.sinkId, streamSize, e);

						errorMetricsByRunId.forEach((runId, recordMetric) -> {
							sendMetricsByRunId(runId, config.table, Optional.empty(), 0L, 0L, recordMetric.errorRecords.get());
							publishExceptionMessage(e, recordMetric.errorRecords.get(), config.table);
						});
						publishMonitoringLog("Error while writing records to destination: " + e.getMessage(), NexlaMonitoringLogType.LOG, NexlaMonitoringLogSeverity.ERROR);
						if (config.stopOnError) {
							throw new RuntimeException(e);
						} else {
							reportTelemetryFromError(e);
						}
					} finally {
						logger.info("flushed buffers, clearing runIds");
						copyOperation.createNewBuffers();
						getAndResetFlushedRunIds();
					}
				}
			}
		}
		return false;
	}

	public interface MetricHandler {
		void handleMetric(String name, Optional<String> displayPath, long numRecords, long numBytes, long numErrors);

		void handleMetrics(FlushResult flushResult);

		void handleQuarantine(NexlaMessage message, Throwable exp, RecordMetric recordMetric);
	}

	protected MetricHandler getMetricHandler() {
		return new MetricHandler() {
			@Override
			public void handleMetric(String name, Optional<String> displayPath, long numRecords, long numBytes, long numErrors) {
				sendMetric(name, displayPath, numRecords, numBytes, numErrors);
			}

			@Override
			public void handleMetrics(FlushResult flushResult) {
				sendMetrics(flushResult);
			}

			@Override
			public void handleQuarantine(NexlaMessage message, Throwable exp, RecordMetric recordMetric) {
				sendToQuarantine(message, exp, recordMetric);
			}
		};
	}

	@Override
	protected void onPipelineFlushed() {
		config.postFlushSqlScript.ifPresent(this::executeSqlScript);
		lastProcessedMessageTs.set(0);
		super.onPipelineFlushed();
	}

	@SneakyThrows
	private void executeSqlScript(String sqlScript) {
		try (Connection connection = probeService.getConnection(config.authConfig);
			 Reader sqlScriptReader = new StringReader(sqlScript)) {
			ScriptRunner scriptRunner = new ScriptRunner(connection);
			scriptRunner.setStopOnError(true);
			scriptRunner.setAutoCommit(connection.getAutoCommit()); // Changes still will be committed after script execution

			logger.info("[SINK-{}] Executing SQL script: '{}'", config.sinkId, sqlScript);
			scriptRunner.runScript(sqlScriptReader);
		} catch (Exception e) {
			throw new Exception("Failed to execute SQL script", e);
		}
	}

	private void sendMetrics(FlushResult flushResult) {
		updateSinkOffsets(flushResult.getBufferOffsets());

		flushResult.getMetricsByRunId().forEach((runId, recordMetric) -> {
			// For no records in sinkBuffer, flushResult does not have runId, so it's set to 0L,
			// so we need to use the global runId in JdbcBaseSink to send metrics
			if (runId == 0L) {
				runId = this.runId;
			}
			sendMetricsByRunId(runId, config.table, Optional.empty(),
				recordMetric.sentRecordsTotal.get(),
				recordMetric.sentBytesTotal.get(),
				recordMetric.errorRecords.get());
			sendQuarantineMessage(config.table, recordMetric.getQuarantineMessages());
		});
	}

	@Override
	protected Set<Long> getActiveRunIds() {
		return runIdsToHeartbeat;
	}

	@Override
	/**
	 * Overridden in CdcSinkTask
	 */
	protected void refreshPipelineStatus() {
		pipelineStatusReport
				.filter(ignored -> copyMode)
				.ifPresent(status -> {
					int bufferedRecords = shouldUseNewCopyFlow()
							? dataWarehouseSink.getCurrentBatchSize() + dataWarehouseSink.getBufferSize()
							: copyOperation.getBuffers().stream().mapToInt(CopyOperationLocalBuffer::streamSize).sum()
							+ batch.size() + batchQueue.stream().mapToInt(dataCollection -> dataCollection.size()).sum();
					status.bufferedRecords.set(bufferedRecords);
				});
		super.refreshPipelineStatus();
	}

	@Override
	protected Optional<Set<Long>> getAndResetFlushedRunIds() {
		Set<Long> heartbeatRunIds = new HashSet<>(runIdsToHeartbeat);
		runIdsToHeartbeat.clear();
		return Optional.of(heartbeatRunIds);
	}

	private void updateSinkOffsets(Map<TopicPartition, Long> offsets) {
		offsetsSender.ifPresent(os -> {
			os.updateSinkOffsets(config.sinkId, offsets);
			logger.info("Updated offsets: {}", offsets);
		});
		offsets.clear();
	}

	private StreamEx<NexlaMessageContext> dedupByPk(StreamEx<NexlaMessageContext> dataStream, List<String> keyFields) {
		LinkedHashMap<List<Object>, NexlaMessageContext> dedupMap = new LinkedHashMap<>();
		dataStream.forEach(value -> dedupMap.put(TableProcessor.extractPrimaryKey(keyFields, value.getMapped().getRawMessage()), value));
		return StreamEx.of(dedupMap.values());
	}

	@SneakyThrows
	private Map<Long, RecordMetric> executeJdbc(Collection<NexlaMessage> records, ExceptionHandler handler) {
		Map<Long, RecordMetric> metricsByRunId = new HashMap<>();

		String tableName = dbDialect.getQualifiedTableName(config.table,
				config.authConfig.schemaName,
				JdbcUtils.getDatabaseName(config));

		List<String> keyFields = config.primaryKey;
		List<String> nonKeyFields = schema.get().fields().stream()
				.map(Field::name)
				.filter(f -> !keyFields.contains(f))
				.collect(toList());

		List<String> allFieldsOrdered = Stream.concat(keyFields.stream(), nonKeyFields.stream()).collect(toList());

		List<String> fieldsForUpdate = new ArrayList<>(nonKeyFields);
		fieldsForUpdate.addAll(keyFields);

		if (config.insertMode == UPSERT) {
			if (dbDialect.getFeatures().contains(MERGE)) {
				if (config.upsertNulls) {
					tableProcessor.doUpsert(records,
							tableName,
							keyFields,
							nonKeyFields,
							allFieldsOrdered,
							metricsByRunId,
							schema.get(),
							handler,
							config);
				} else {
					tableProcessor.doUpsertWithoutNull(records,
							tableName,
							keyFields,
							nonKeyFields,
							allFieldsOrdered,
							metricsByRunId,
							schema.get(),
							handler,
							config);
				}
			} else {
				tableProcessor.doUpsertEmulation(records,
						tableName,
						keyFields,
						nonKeyFields,
						fieldsForUpdate,
						metricsByRunId,
						schema.get(),
						handler,
						config);
			}
		} else if (config.insertMode == INSERT) {
			tableProcessor.doInsert(
					records,
					tableName,
					keyFields,
					nonKeyFields,
					allFieldsOrdered,
					metricsByRunId,
					schema.get(),
					handler,
					config);
		}
		return metricsByRunId;
	}

	@Override
	public void stop() throws ConnectException {
		if (dataWarehouseSink != null) {
			dataWarehouseSink.onConnectorStop(connProvider);
		}
		if (probeService != null) {
			probeService.close();
		}
		if (executor != null) {
			executor.shutdown();
		}
		if (copyOperation != null) {
			copyOperation.close(connProvider);
		}
		batch.closeResources();
		super.stop();
	}

	@Override
	public Optional<BaseAuthConfig> authConfig() {
		return of(config.authConfig);
	}

	public List<HostPort> getHostPorts() {
		if (config.authConfig.url != null && !config.authConfig.url.isEmpty()
				&& (config.authConfig.host == null || config.authConfig.port == null)) {
			Optional<HostPort> hostPort = JdbcAuthConfig.extractHostPortFromUrl(config.authConfig.getCredsId(), config.authConfig.url);
			if (hostPort.isPresent()) {
				return Lists.newArrayList(hostPort.get());
			}
		}
		return Lists.newArrayList(new HostPort(config.authConfig.host, config.authConfig.port));
	}

	protected boolean shouldUseNewCopyFlow() {
		return TYPES_ENABLED_FOR_NEW_FLOW.contains(config.authConfig.dbType);
	}

	@Override
	protected boolean isSinkSpecificTelemetryRetriableError(Throwable e) {
		return e instanceof JdbcConnectionException || e instanceof SqlConnectionException;
	}
}
