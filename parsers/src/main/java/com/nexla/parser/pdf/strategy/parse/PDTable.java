package com.nexla.parser.pdf.strategy.parse;

import com.nexla.parser.pdf.strategy.model.TableBlock;
import com.nexla.parser.pdf.strategy.parse.geom.Rect;
import lombok.*;
import org.apache.pdfbox.text.TextPosition;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Getter
@Setter
public class PDTable implements TableBlock, BoundBlock {
    private Rectangle tableArea;
    private Tuple header;
    private java.util.List<VBounds> vBounds = new ArrayList<>();
    private java.util.List<Tuple> tuples = new ArrayList<>();

    private java.util.List<TextPosition> textPositions;

    public PDTable(Rectangle tableArea, Tuple header, List<TextPosition> textPositions) {
        this.tableArea = tableArea;
        this.header = header;
        this.textPositions = textPositions;
    }

    @Override
    public Rectangle getBoundingBox() {
        return tableArea;
    }

    @Getter
    public static class Tuple implements TableBlock.Tuple {
        private final Integer pageNumber;
        private final List<Cell> cells;

        public Tuple(int pageNumber, List<Cell> cells) {
            this.pageNumber = pageNumber;
            this.cells = cells;
        }

        public Rectangle getBoundingBox() {
            if (cells.isEmpty()) {
                return null;
            }

            Optional<Rectangle> optBB = cells.stream().filter(Objects::nonNull).map(Cell::getBoundingBox).filter(Objects::nonNull).findFirst();
            if (optBB.isEmpty()) {
                return null;
            }

            Rectangle bb = optBB.get();
            for (Cell cell : cells) {
                if (cell != null && cell.getBoundingBox() != null) {
                    bb = Rect.merge(bb, cell.getBoundingBox());
                }
            }

            return bb;
        }

        public Optional<Cell> findCellByValue(List<Cell> cells, String v) {
            return cells.stream().filter(c -> c.getValue().equals(v)).findFirst();
        }

        @Override
        public Optional<TableBlock.Cell> get(int i) {
            return cells.size() > i ? Optional.ofNullable(cells.get(i)) : Optional.empty();
        }

    }

    @Data
    @AllArgsConstructor
    public static class VBounds {
        private final Cell cell;
        private final Rectangle boundingBox;
    }

    @Getter
    @Setter
    @ToString
    public static class Cell implements TableBlock.Cell {
        private String value;
        private Rectangle boundingBox;

        public Cell(String value, Rectangle boundingBox) {
            this.value = value;
            this.boundingBox = boundingBox;
        }
    }
}
