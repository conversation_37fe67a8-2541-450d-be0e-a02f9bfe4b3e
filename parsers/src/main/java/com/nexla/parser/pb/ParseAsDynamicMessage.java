package com.nexla.parser.pb;

import com.google.protobuf.DescriptorProtos;
import com.google.protobuf.Descriptors;
import com.google.protobuf.DynamicMessage;
import com.nexla.common.NexlaMessage;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

class ParseAsDynamicMessage implements ParsingStrategy {
    private final byte[] fileSetDescriptor;
    private final String rootType;

    public ParseAsDynamicMessage(byte[] fileSetDescriptor, String rootType) {
        this.fileSetDescriptor = fileSetDescriptor;
        this.rootType = rootType;
    }

    @Override
    public Optional<Stream<NexlaMessage>> parse(byte[] message) {
        if (fileSetDescriptor == null || rootType == null) {
            return Optional.empty();
        }

        Descriptors.Descriptor descriptor = descriptorOf(rootType);
        if (descriptor == null) {
            return Optional.empty();
        }

        return parseAsSingleMessage(message, descriptor)
                .or(() -> parseAsDelimited(message, descriptor));
    }

    private Optional<Stream<NexlaMessage>> parseAsDelimited(byte[] message, Descriptors.Descriptor descriptor) {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(message);

        List<NexlaMessage> result = new ArrayList<>();
        try {
            do {
                DynamicMessage.Builder builder = DynamicMessage.newBuilder(descriptor);
                if (!builder.mergeDelimitedFrom(inputStream)) {
                    break; // EOS
                }

                DynamicMessage parsed = builder.build();
                if (!valid(parsed)) {
                    continue;
                }

                result.add(new NexlaMessage(Protobuf.toMap(parsed)));
            } while (inputStream.available() != 0);
        } catch (IOException e) { /* ignored */ }

        return ifStreamNonEmpty(result);
    }

    private Optional<Stream<NexlaMessage>> ifStreamNonEmpty(List<NexlaMessage> result) {
        if (result.isEmpty()) {
            return Optional.empty();
        }

        return Optional.of(result.stream());
    }

    private Optional<Stream<NexlaMessage>> parseAsSingleMessage(byte[] message, Descriptors.Descriptor descriptor) {
        try {
            DynamicMessage parsed = DynamicMessage.parseFrom(descriptor, message);
            if (!valid(parsed)) {
                return Optional.empty();
            }

            return Optional.of(Stream.of(new NexlaMessage(Protobuf.toMap(parsed))));
        } catch (IOException e) {
            return Optional.empty();
        }
    }

    private boolean valid(DynamicMessage parsed) {
        return parsed != null && !parsed.getAllFields().isEmpty();
    }

    private Descriptors.Descriptor descriptorOf(String rootType) {
        try {
            DescriptorProtos.FileDescriptorSet fds = DescriptorProtos.FileDescriptorSet
                    .parseFrom(fileSetDescriptor);

            if (fds.getFileCount() == 0) {
                return null;
            }

            return Descriptors.FileDescriptor.buildFrom(fds.getFile(0), new Descriptors.FileDescriptor[]{})
                    .findMessageTypeByName(rootType);
        } catch (IOException | Descriptors.DescriptorValidationException e) {
            return null;
        }
    }
}
