{"env": {"CONNECT_EXTERNAL_KAFKA_STATSD_HOST": "datadog-agent.marathon.l4lb.thisdcos.directory", "CONNECT_KEY_CONVERTER_SCHEMA_REGISTRY_URL": "http://schema-registry.marathon.l4lb.thisdcos.directory:8081", "CONNECT_KEY_CONVERTER": "org.apache.kafka.connect.storage.StringConverter", "CONNECT_METRIC_REPORTERS": "com.airbnb.kafka.kafka09.StatsdMetricsReporter", "CONNECT_VALUE_CONVERTER_SCHEMA_REGISTRY_URL": "http://schema-registry.marathon.l4lb.thisdcos.directory:8081", "CONNECT_REST_PORT": "8083", "CONNECT_BOOTSTRAP_SERVERS": "broker.kafka.l4lb.thisdcos.directory:9092", "CONNECT_GROUP_ID": "dcos-connect-groupsink-poll-bigq", "CONNECT_INTERNAL_KEY_CONVERTER": "org.apache.kafka.connect.json.JsonConverter", "CONNECT_STATUS_STORAGE_TOPIC": "dcos-connect-statussink-poll-bigq", "CONNECT_VALUE_CONVERTER": "org.apache.kafka.connect.storage.StringConverter", "CONNECT_ZOOKEEPER_CONNECT": "zookeeper-0-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140,zookeeper-1-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140,zookeeper-2-server.kafka-zookeeper.autoip.dcos.thisdcos.directory:1140", "CONNECT_INTERNAL_VALUE_CONVERTER": "org.apache.kafka.connect.json.JsonConverter", "CONNECT_OFFSET_STORAGE_TOPIC": "dcos-connect-offsetssink-poll-bigq", "CONNECT_CONFIG_STORAGE_TOPIC": "dcos-connect-configssink-poll-bigq", "CONNECT_EXTERNAL_KAFKA_STATSD_REPORTER_ENABLED": "true", "CONNECT_EXTERNAL_KAFKA_STATSD_TAG_ENABLED": "true"}, "labels": {"DCOS_SERVICE_SCHEME": "http", "DCOS_SERVICE_PORT_INDEX": "0", "DCOS_SERVICE_NAME": "sink-bigquery-connector", "DCOS_PACKAGE_IS_FRAMEWORK": "false"}, "id": "/sink-bigquery-connector", "backoffFactor": 1.15, "backoffSeconds": 1, "container": {"portMappings": [{"containerPort": 8083, "hostPort": 0, "labels": {"VIP_0": "/sink-bigquery-connector:8083"}, "protocol": "tcp", "servicePort": 11009, "name": "default"}, {"containerPort": 2000, "hostPort": 0, "protocol": "tcp", "servicePort": 10126}], "type": "DOCKER", "volumes": [], "docker": {"image": "nexla/kafka-connect-bigquery-sink:develop-latest", "forcePullImage": true, "privileged": false, "parameters": []}}, "cpus": 0.25, "disk": 0, "healthChecks": [{"gracePeriodSeconds": 300, "intervalSeconds": 60, "maxConsecutiveFailures": 3, "portIndex": 0, "timeoutSeconds": 20, "delaySeconds": 15, "protocol": "MESOS_HTTP", "path": "/", "ipProtocol": "IPv4"}], "instances": 1, "maxLaunchDelaySeconds": 3600, "mem": 4096, "gpus": 0, "networks": [{"mode": "container/bridge"}], "requirePorts": false, "taskKillGracePeriodSeconds": 20, "upgradeStrategy": {"maximumOverCapacity": 1, "minimumHealthCapacity": 1}, "killSelection": "YOUNGEST_FIRST", "unreachableStrategy": {"inactiveAfterSeconds": 0, "expungeAfterSeconds": 0}, "fetch": [], "constraints": []}