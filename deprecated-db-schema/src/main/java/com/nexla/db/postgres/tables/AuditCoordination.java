/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables;

import com.nexla.db.postgres.DefaultSchema;
import com.nexla.db.postgres.Keys;
import com.nexla.db.postgres.tables.records.AuditCoordinationRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class AuditCoordination extends TableImpl<AuditCoordinationRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>audit_coordination</code> */
  public static final AuditCoordination AUDIT_COORDINATION = new AuditCoordination();

  /** The class holding records for this type */
  @Override
  public Class<AuditCoordinationRecord> getRecordType() {
    return AuditCoordinationRecord.class;
  }

  /** The column <code>audit_coordination.id</code>. */
  public final TableField<AuditCoordinationRecord, Long> ID =
      createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

  /** The column <code>audit_coordination.message_id</code>. */
  public final TableField<AuditCoordinationRecord, String> MESSAGE_ID =
      createField(DSL.name("message_id"), SQLDataType.VARCHAR(100), this, "");

  /** The column <code>audit_coordination.event_type</code>. */
  public final TableField<AuditCoordinationRecord, String> EVENT_TYPE =
      createField(DSL.name("event_type"), SQLDataType.VARCHAR(30).nullable(false), this, "");

  /** The column <code>audit_coordination.created_at</code>. */
  public final TableField<AuditCoordinationRecord, LocalDateTime> CREATED_AT =
      createField(DSL.name("created_at"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "");

  /** The column <code>audit_coordination.body</code>. */
  public final TableField<AuditCoordinationRecord, String> BODY =
      createField(DSL.name("body"), SQLDataType.CLOB.nullable(false), this, "");

  /** The column <code>audit_coordination.audit_ts</code>. */
  public final TableField<AuditCoordinationRecord, LocalDateTime> AUDIT_TS =
      createField(
          DSL.name("audit_ts"),
          SQLDataType.LOCALDATETIME(0)
              .nullable(false)
              .defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)),
          this,
          "");

  private AuditCoordination(Name alias, Table<AuditCoordinationRecord> aliased) {
    this(alias, aliased, null);
  }

  private AuditCoordination(
      Name alias, Table<AuditCoordinationRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>audit_coordination</code> table reference */
  public AuditCoordination(String alias) {
    this(DSL.name(alias), AUDIT_COORDINATION);
  }

  /** Create an aliased <code>audit_coordination</code> table reference */
  public AuditCoordination(Name alias) {
    this(alias, AUDIT_COORDINATION);
  }

  /** Create a <code>audit_coordination</code> table reference */
  public AuditCoordination() {
    this(DSL.name("audit_coordination"), null);
  }

  public <O extends Record> AuditCoordination(
      Table<O> child, ForeignKey<O, AuditCoordinationRecord> key) {
    super(child, key, AUDIT_COORDINATION);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public Identity<AuditCoordinationRecord, Long> getIdentity() {
    return (Identity<AuditCoordinationRecord, Long>) super.getIdentity();
  }

  @Override
  public UniqueKey<AuditCoordinationRecord> getPrimaryKey() {
    return Keys.KEY_AUDIT_COORDINATION_PRIMARY;
  }

  @Override
  public List<UniqueKey<AuditCoordinationRecord>> getKeys() {
    return Arrays.<UniqueKey<AuditCoordinationRecord>>asList(Keys.KEY_AUDIT_COORDINATION_PRIMARY);
  }

  @Override
  public AuditCoordination as(String alias) {
    return new AuditCoordination(DSL.name(alias), this);
  }

  @Override
  public AuditCoordination as(Name alias) {
    return new AuditCoordination(alias, this);
  }

  /** Rename this table */
  @Override
  public AuditCoordination rename(String name) {
    return new AuditCoordination(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public AuditCoordination rename(Name name) {
    return new AuditCoordination(name, null);
  }

  // -------------------------------------------------------------------------
  // Row6 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row6<Long, String, String, LocalDateTime, String, LocalDateTime> fieldsRow() {
    return (Row6) super.fieldsRow();
  }
}
