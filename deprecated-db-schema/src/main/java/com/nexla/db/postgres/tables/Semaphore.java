/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables;

import com.nexla.db.postgres.DefaultSchema;
import com.nexla.db.postgres.Keys;
import com.nexla.db.postgres.tables.records.SemaphoreRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row11;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class Semaphore extends TableImpl<SemaphoreRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>semaphore</code> */
  public static final Semaphore SEMAPHORE = new Semaphore();

  /** The class holding records for this type */
  @Override
  public Class<SemaphoreRecord> getRecordType() {
    return SemaphoreRecord.class;
  }

  /** The column <code>semaphore.name</code>. */
  public final TableField<SemaphoreRecord, String> NAME =
      createField(DSL.name("name"), SQLDataType.VARCHAR(255).nullable(false), this, "");

  /** The column <code>semaphore.type</code>. */
  public final TableField<SemaphoreRecord, String> TYPE =
      createField(DSL.name("type"), SQLDataType.VARCHAR(36).nullable(false), this, "");

  /** The column <code>semaphore.status</code>. */
  public final TableField<SemaphoreRecord, String> STATUS =
      createField(DSL.name("status"), SQLDataType.VARCHAR(36).nullable(false), this, "");

  /** The column <code>semaphore.acquired_at</code>. */
  public final TableField<SemaphoreRecord, LocalDateTime> ACQUIRED_AT =
      createField(DSL.name("acquired_at"), SQLDataType.LOCALDATETIME(3).nullable(false), this, "");

  /** The column <code>semaphore.expires_at</code>. */
  public final TableField<SemaphoreRecord, LocalDateTime> EXPIRES_AT =
      createField(DSL.name("expires_at"), SQLDataType.LOCALDATETIME(3).nullable(false), this, "");

  /** The column <code>semaphore.finished_at</code>. */
  public final TableField<SemaphoreRecord, LocalDateTime> FINISHED_AT =
      createField(DSL.name("finished_at"), SQLDataType.LOCALDATETIME(3), this, "");

  /** The column <code>semaphore.success_at</code>. */
  public final TableField<SemaphoreRecord, LocalDateTime> SUCCESS_AT =
      createField(DSL.name("success_at"), SQLDataType.LOCALDATETIME(3), this, "");

  /** The column <code>semaphore.resource_type</code>. */
  public final TableField<SemaphoreRecord, String> RESOURCE_TYPE =
      createField(DSL.name("resource_type"), SQLDataType.VARCHAR(10), this, "");

  /** The column <code>semaphore.resource_id</code>. */
  public final TableField<SemaphoreRecord, Integer> RESOURCE_ID =
      createField(DSL.name("resource_id"), SQLDataType.INTEGER, this, "");

  /** The column <code>semaphore.pod_name</code>. */
  public final TableField<SemaphoreRecord, String> POD_NAME =
      createField(DSL.name("pod_name"), SQLDataType.VARCHAR(70), this, "");

  /** The column <code>semaphore.message</code>. */
  public final TableField<SemaphoreRecord, String> MESSAGE =
      createField(DSL.name("message"), SQLDataType.CLOB, this, "");

  private Semaphore(Name alias, Table<SemaphoreRecord> aliased) {
    this(alias, aliased, null);
  }

  private Semaphore(Name alias, Table<SemaphoreRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>semaphore</code> table reference */
  public Semaphore(String alias) {
    this(DSL.name(alias), SEMAPHORE);
  }

  /** Create an aliased <code>semaphore</code> table reference */
  public Semaphore(Name alias) {
    this(alias, SEMAPHORE);
  }

  /** Create a <code>semaphore</code> table reference */
  public Semaphore() {
    this(DSL.name("semaphore"), null);
  }

  public <O extends Record> Semaphore(Table<O> child, ForeignKey<O, SemaphoreRecord> key) {
    super(child, key, SEMAPHORE);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public UniqueKey<SemaphoreRecord> getPrimaryKey() {
    return Keys.KEY_SEMAPHORE_PRIMARY;
  }

  @Override
  public List<UniqueKey<SemaphoreRecord>> getKeys() {
    return Arrays.<UniqueKey<SemaphoreRecord>>asList(Keys.KEY_SEMAPHORE_PRIMARY);
  }

  @Override
  public Semaphore as(String alias) {
    return new Semaphore(DSL.name(alias), this);
  }

  @Override
  public Semaphore as(Name alias) {
    return new Semaphore(alias, this);
  }

  /** Rename this table */
  @Override
  public Semaphore rename(String name) {
    return new Semaphore(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public Semaphore rename(Name name) {
    return new Semaphore(name, null);
  }

  // -------------------------------------------------------------------------
  // Row11 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row11<
          String,
          String,
          String,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          String,
          Integer,
          String,
          String>
      fieldsRow() {
    return (Row11) super.fieldsRow();
  }
}
