/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.postgres.tables.records;

import com.nexla.db.postgres.tables.PipelineTask;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class PipelineTaskRecord extends UpdatableRecordImpl<PipelineTaskRecord>
    implements Record10<
        String,
        String,
        Integer,
        Boolean,
        String,
        String,
        LocalDateTime,
        LocalDateTime,
        LocalDateTime,
        Boolean> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>pipeline_task.task_id</code>. */
  public void setTaskId(String value) {
    set(0, value);
  }

  /** Getter for <code>pipeline_task.task_id</code>. */
  public String getTaskId() {
    return (String) get(0);
  }

  /** Setter for <code>pipeline_task.task_type</code>. */
  public void setTaskType(String value) {
    set(1, value);
  }

  /** Getter for <code>pipeline_task.task_type</code>. */
  public String getTaskType() {
    return (String) get(1);
  }

  /** Setter for <code>pipeline_task.max_instances</code>. */
  public void setMaxInstances(Integer value) {
    set(2, value);
  }

  /** Getter for <code>pipeline_task.max_instances</code>. */
  public Integer getMaxInstances() {
    return (Integer) get(2);
  }

  /** Setter for <code>pipeline_task.dedicated</code>. */
  public void setDedicated(Boolean value) {
    set(3, value);
  }

  /** Getter for <code>pipeline_task.dedicated</code>. */
  public Boolean getDedicated() {
    return (Boolean) get(3);
  }

  /** Setter for <code>pipeline_task.state</code>. */
  public void setState(String value) {
    set(4, value);
  }

  /** Getter for <code>pipeline_task.state</code>. */
  public String getState() {
    return (String) get(4);
  }

  /** Setter for <code>pipeline_task.meta</code>. */
  public void setMeta(String value) {
    set(5, value);
  }

  /** Getter for <code>pipeline_task.meta</code>. */
  public String getMeta() {
    return (String) get(5);
  }

  /** Setter for <code>pipeline_task.heartbeat_ts</code>. */
  public void setHeartbeatTs(LocalDateTime value) {
    set(6, value);
  }

  /** Getter for <code>pipeline_task.heartbeat_ts</code>. */
  public LocalDateTime getHeartbeatTs() {
    return (LocalDateTime) get(6);
  }

  /** Setter for <code>pipeline_task.last_active_ts</code>. */
  public void setLastActiveTs(LocalDateTime value) {
    set(7, value);
  }

  /** Getter for <code>pipeline_task.last_active_ts</code>. */
  public LocalDateTime getLastActiveTs() {
    return (LocalDateTime) get(7);
  }

  /** Setter for <code>pipeline_task.last_data_ts</code>. */
  public void setLastDataTs(LocalDateTime value) {
    set(8, value);
  }

  /** Getter for <code>pipeline_task.last_data_ts</code>. */
  public LocalDateTime getLastDataTs() {
    return (LocalDateTime) get(8);
  }

  /** Setter for <code>pipeline_task.excluded</code>. */
  public void setExcluded(Boolean value) {
    set(9, value);
  }

  /** Getter for <code>pipeline_task.excluded</code>. */
  public Boolean getExcluded() {
    return (Boolean) get(9);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<String> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record10 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row10<
          String,
          String,
          Integer,
          Boolean,
          String,
          String,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          Boolean>
      fieldsRow() {
    return (Row10) super.fieldsRow();
  }

  @Override
  public Row10<
          String,
          String,
          Integer,
          Boolean,
          String,
          String,
          LocalDateTime,
          LocalDateTime,
          LocalDateTime,
          Boolean>
      valuesRow() {
    return (Row10) super.valuesRow();
  }

  @Override
  public Field<String> field1() {
    return PipelineTask.PIPELINE_TASK.TASK_ID;
  }

  @Override
  public Field<String> field2() {
    return PipelineTask.PIPELINE_TASK.TASK_TYPE;
  }

  @Override
  public Field<Integer> field3() {
    return PipelineTask.PIPELINE_TASK.MAX_INSTANCES;
  }

  @Override
  public Field<Boolean> field4() {
    return PipelineTask.PIPELINE_TASK.DEDICATED;
  }

  @Override
  public Field<String> field5() {
    return PipelineTask.PIPELINE_TASK.STATE;
  }

  @Override
  public Field<String> field6() {
    return PipelineTask.PIPELINE_TASK.META;
  }

  @Override
  public Field<LocalDateTime> field7() {
    return PipelineTask.PIPELINE_TASK.HEARTBEAT_TS;
  }

  @Override
  public Field<LocalDateTime> field8() {
    return PipelineTask.PIPELINE_TASK.LAST_ACTIVE_TS;
  }

  @Override
  public Field<LocalDateTime> field9() {
    return PipelineTask.PIPELINE_TASK.LAST_DATA_TS;
  }

  @Override
  public Field<Boolean> field10() {
    return PipelineTask.PIPELINE_TASK.EXCLUDED;
  }

  @Override
  public String component1() {
    return getTaskId();
  }

  @Override
  public String component2() {
    return getTaskType();
  }

  @Override
  public Integer component3() {
    return getMaxInstances();
  }

  @Override
  public Boolean component4() {
    return getDedicated();
  }

  @Override
  public String component5() {
    return getState();
  }

  @Override
  public String component6() {
    return getMeta();
  }

  @Override
  public LocalDateTime component7() {
    return getHeartbeatTs();
  }

  @Override
  public LocalDateTime component8() {
    return getLastActiveTs();
  }

  @Override
  public LocalDateTime component9() {
    return getLastDataTs();
  }

  @Override
  public Boolean component10() {
    return getExcluded();
  }

  @Override
  public String value1() {
    return getTaskId();
  }

  @Override
  public String value2() {
    return getTaskType();
  }

  @Override
  public Integer value3() {
    return getMaxInstances();
  }

  @Override
  public Boolean value4() {
    return getDedicated();
  }

  @Override
  public String value5() {
    return getState();
  }

  @Override
  public String value6() {
    return getMeta();
  }

  @Override
  public LocalDateTime value7() {
    return getHeartbeatTs();
  }

  @Override
  public LocalDateTime value8() {
    return getLastActiveTs();
  }

  @Override
  public LocalDateTime value9() {
    return getLastDataTs();
  }

  @Override
  public Boolean value10() {
    return getExcluded();
  }

  @Override
  public PipelineTaskRecord value1(String value) {
    setTaskId(value);
    return this;
  }

  @Override
  public PipelineTaskRecord value2(String value) {
    setTaskType(value);
    return this;
  }

  @Override
  public PipelineTaskRecord value3(Integer value) {
    setMaxInstances(value);
    return this;
  }

  @Override
  public PipelineTaskRecord value4(Boolean value) {
    setDedicated(value);
    return this;
  }

  @Override
  public PipelineTaskRecord value5(String value) {
    setState(value);
    return this;
  }

  @Override
  public PipelineTaskRecord value6(String value) {
    setMeta(value);
    return this;
  }

  @Override
  public PipelineTaskRecord value7(LocalDateTime value) {
    setHeartbeatTs(value);
    return this;
  }

  @Override
  public PipelineTaskRecord value8(LocalDateTime value) {
    setLastActiveTs(value);
    return this;
  }

  @Override
  public PipelineTaskRecord value9(LocalDateTime value) {
    setLastDataTs(value);
    return this;
  }

  @Override
  public PipelineTaskRecord value10(Boolean value) {
    setExcluded(value);
    return this;
  }

  @Override
  public PipelineTaskRecord values(
      String value1,
      String value2,
      Integer value3,
      Boolean value4,
      String value5,
      String value6,
      LocalDateTime value7,
      LocalDateTime value8,
      LocalDateTime value9,
      Boolean value10) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached PipelineTaskRecord */
  public PipelineTaskRecord() {
    super(PipelineTask.PIPELINE_TASK);
  }

  /**
   * Create a detached, initialised PipelineTaskRecord
   *
   * <p>DO NOT USE ALL ARGS CONSTRUCTOR for DB *Record classes
   */
  public PipelineTaskRecord(
      String taskId,
      String taskType,
      Integer maxInstances,
      Boolean dedicated,
      String state,
      String meta,
      LocalDateTime heartbeatTs,
      LocalDateTime lastActiveTs,
      LocalDateTime lastDataTs,
      Boolean excluded) {
    super(PipelineTask.PIPELINE_TASK);

    setTaskId(taskId);
    setTaskType(taskType);
    setMaxInstances(maxInstances);
    setDedicated(dedicated);
    setState(state);
    setMeta(meta);
    setHeartbeatTs(heartbeatTs);
    setLastActiveTs(lastActiveTs);
    setLastDataTs(lastDataTs);
    setExcluded(excluded);
  }
}
