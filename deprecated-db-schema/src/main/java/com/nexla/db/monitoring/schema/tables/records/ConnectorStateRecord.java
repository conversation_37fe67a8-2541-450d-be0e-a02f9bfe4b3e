/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.tables.ConnectorState;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class ConnectorStateRecord extends UpdatableRecordImpl<ConnectorStateRecord>
    implements Record5<String, Integer, String, String, LocalDateTime> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>connector_state.resource_type</code>. */
  public void setResourceType(String value) {
    set(0, value);
  }

  /** Getter for <code>connector_state.resource_type</code>. */
  public String getResourceType() {
    return (String) get(0);
  }

  /** Setter for <code>connector_state.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(1, value);
  }

  /** Getter for <code>connector_state.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(1);
  }

  /** Setter for <code>connector_state.state</code>. */
  public void setState(String value) {
    set(2, value);
  }

  /** Getter for <code>connector_state.state</code>. */
  public String getState() {
    return (String) get(2);
  }

  /** Setter for <code>connector_state.message</code>. */
  public void setMessage(String value) {
    set(3, value);
  }

  /** Getter for <code>connector_state.message</code>. */
  public String getMessage() {
    return (String) get(3);
  }

  /** Setter for <code>connector_state.last_modified</code>. */
  public void setLastModified(LocalDateTime value) {
    set(4, value);
  }

  /** Getter for <code>connector_state.last_modified</code>. */
  public LocalDateTime getLastModified() {
    return (LocalDateTime) get(4);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record2<String, Integer> key() {
    return (Record2) super.key();
  }

  // -------------------------------------------------------------------------
  // Record5 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row5<String, Integer, String, String, LocalDateTime> fieldsRow() {
    return (Row5) super.fieldsRow();
  }

  @Override
  public Row5<String, Integer, String, String, LocalDateTime> valuesRow() {
    return (Row5) super.valuesRow();
  }

  @Override
  public Field<String> field1() {
    return ConnectorState.CONNECTOR_STATE.RESOURCE_TYPE;
  }

  @Override
  public Field<Integer> field2() {
    return ConnectorState.CONNECTOR_STATE.RESOURCE_ID;
  }

  @Override
  public Field<String> field3() {
    return ConnectorState.CONNECTOR_STATE.STATE;
  }

  @Override
  public Field<String> field4() {
    return ConnectorState.CONNECTOR_STATE.MESSAGE;
  }

  @Override
  public Field<LocalDateTime> field5() {
    return ConnectorState.CONNECTOR_STATE.LAST_MODIFIED;
  }

  @Override
  public String component1() {
    return getResourceType();
  }

  @Override
  public Integer component2() {
    return getResourceId();
  }

  @Override
  public String component3() {
    return getState();
  }

  @Override
  public String component4() {
    return getMessage();
  }

  @Override
  public LocalDateTime component5() {
    return getLastModified();
  }

  @Override
  public String value1() {
    return getResourceType();
  }

  @Override
  public Integer value2() {
    return getResourceId();
  }

  @Override
  public String value3() {
    return getState();
  }

  @Override
  public String value4() {
    return getMessage();
  }

  @Override
  public LocalDateTime value5() {
    return getLastModified();
  }

  @Override
  public ConnectorStateRecord value1(String value) {
    setResourceType(value);
    return this;
  }

  @Override
  public ConnectorStateRecord value2(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public ConnectorStateRecord value3(String value) {
    setState(value);
    return this;
  }

  @Override
  public ConnectorStateRecord value4(String value) {
    setMessage(value);
    return this;
  }

  @Override
  public ConnectorStateRecord value5(LocalDateTime value) {
    setLastModified(value);
    return this;
  }

  @Override
  public ConnectorStateRecord values(
      String value1, Integer value2, String value3, String value4, LocalDateTime value5) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached ConnectorStateRecord */
  public ConnectorStateRecord() {
    super(ConnectorState.CONNECTOR_STATE);
  }

  /** Create a detached, initialised ConnectorStateRecord */
  public ConnectorStateRecord(
      String resourceType,
      Integer resourceId,
      String state,
      String message,
      LocalDateTime lastModified) {
    super(ConnectorState.CONNECTOR_STATE);

    setResourceType(resourceType);
    setResourceId(resourceId);
    setState(state);
    setMessage(message);
    setLastModified(lastModified);
  }
}
