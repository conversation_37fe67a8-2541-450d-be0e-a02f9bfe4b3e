/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.enums;

import org.jooq.Catalog;
import org.jooq.EnumType;
import org.jooq.Schema;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public enum ErrorStatusMetricsStatus implements EnumType {
  ERROR("ERROR"),

  OK("OK"),

  WARNING("WARNING");

  private final String literal;

  private ErrorStatusMetricsStatus(String literal) {
    this.literal = literal;
  }

  @Override
  public Catalog getCatalog() {
    return null;
  }

  @Override
  public Schema getSchema() {
    return null;
  }

  @Override
  public String getName() {
    return "error_status_metrics_status";
  }

  @Override
  public String getLiteral() {
    return literal;
  }
}
