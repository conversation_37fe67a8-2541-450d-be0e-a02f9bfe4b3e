/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.tables.QuarantineTopicOffset;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record4;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class QuarantineTopicOffsetRecord extends UpdatableRecordImpl<QuarantineTopicOffsetRecord>
    implements Record7<String, Integer, Integer, Integer, String, Long, LocalDateTime> {

  private static final long serialVersionUID = 1L;

  /** Setter for <code>quarantine_topic_offset.resource_type</code>. */
  public void setResourceType(String value) {
    set(0, value);
  }

  /** Getter for <code>quarantine_topic_offset.resource_type</code>. */
  public String getResourceType() {
    return (String) get(0);
  }

  /** Setter for <code>quarantine_topic_offset.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(1, value);
  }

  /** Getter for <code>quarantine_topic_offset.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(1);
  }

  /** Setter for <code>quarantine_topic_offset.dataset_id</code>. */
  public void setDatasetId(Integer value) {
    set(2, value);
  }

  /** Getter for <code>quarantine_topic_offset.dataset_id</code>. */
  public Integer getDatasetId() {
    return (Integer) get(2);
  }

  /** Setter for <code>quarantine_topic_offset.partition_number</code>. */
  public void setPartitionNumber(Integer value) {
    set(3, value);
  }

  /** Getter for <code>quarantine_topic_offset.partition_number</code>. */
  public Integer getPartitionNumber() {
    return (Integer) get(3);
  }

  /** Setter for <code>quarantine_topic_offset.topic</code>. */
  public void setTopic(String value) {
    set(4, value);
  }

  /** Getter for <code>quarantine_topic_offset.topic</code>. */
  public String getTopic() {
    return (String) get(4);
  }

  /** Setter for <code>quarantine_topic_offset.offset</code>. */
  public void setOffset(Long value) {
    set(5, value);
  }

  /** Getter for <code>quarantine_topic_offset.offset</code>. */
  public Long getOffset() {
    return (Long) get(5);
  }

  /** Setter for <code>quarantine_topic_offset.last_modified</code>. */
  public void setLastModified(LocalDateTime value) {
    set(6, value);
  }

  /** Getter for <code>quarantine_topic_offset.last_modified</code>. */
  public LocalDateTime getLastModified() {
    return (LocalDateTime) get(6);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record4<String, Integer, String, Integer> key() {
    return (Record4) super.key();
  }

  // -------------------------------------------------------------------------
  // Record7 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row7<String, Integer, Integer, Integer, String, Long, LocalDateTime> fieldsRow() {
    return (Row7) super.fieldsRow();
  }

  @Override
  public Row7<String, Integer, Integer, Integer, String, Long, LocalDateTime> valuesRow() {
    return (Row7) super.valuesRow();
  }

  @Override
  public Field<String> field1() {
    return QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET.RESOURCE_TYPE;
  }

  @Override
  public Field<Integer> field2() {
    return QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET.RESOURCE_ID;
  }

  @Override
  public Field<Integer> field3() {
    return QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET.DATASET_ID;
  }

  @Override
  public Field<Integer> field4() {
    return QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET.PARTITION_NUMBER;
  }

  @Override
  public Field<String> field5() {
    return QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET.TOPIC;
  }

  @Override
  public Field<Long> field6() {
    return QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET.OFFSET;
  }

  @Override
  public Field<LocalDateTime> field7() {
    return QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET.LAST_MODIFIED;
  }

  @Override
  public String component1() {
    return getResourceType();
  }

  @Override
  public Integer component2() {
    return getResourceId();
  }

  @Override
  public Integer component3() {
    return getDatasetId();
  }

  @Override
  public Integer component4() {
    return getPartitionNumber();
  }

  @Override
  public String component5() {
    return getTopic();
  }

  @Override
  public Long component6() {
    return getOffset();
  }

  @Override
  public LocalDateTime component7() {
    return getLastModified();
  }

  @Override
  public String value1() {
    return getResourceType();
  }

  @Override
  public Integer value2() {
    return getResourceId();
  }

  @Override
  public Integer value3() {
    return getDatasetId();
  }

  @Override
  public Integer value4() {
    return getPartitionNumber();
  }

  @Override
  public String value5() {
    return getTopic();
  }

  @Override
  public Long value6() {
    return getOffset();
  }

  @Override
  public LocalDateTime value7() {
    return getLastModified();
  }

  @Override
  public QuarantineTopicOffsetRecord value1(String value) {
    setResourceType(value);
    return this;
  }

  @Override
  public QuarantineTopicOffsetRecord value2(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public QuarantineTopicOffsetRecord value3(Integer value) {
    setDatasetId(value);
    return this;
  }

  @Override
  public QuarantineTopicOffsetRecord value4(Integer value) {
    setPartitionNumber(value);
    return this;
  }

  @Override
  public QuarantineTopicOffsetRecord value5(String value) {
    setTopic(value);
    return this;
  }

  @Override
  public QuarantineTopicOffsetRecord value6(Long value) {
    setOffset(value);
    return this;
  }

  @Override
  public QuarantineTopicOffsetRecord value7(LocalDateTime value) {
    setLastModified(value);
    return this;
  }

  @Override
  public QuarantineTopicOffsetRecord values(
      String value1,
      Integer value2,
      Integer value3,
      Integer value4,
      String value5,
      Long value6,
      LocalDateTime value7) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached QuarantineTopicOffsetRecord */
  public QuarantineTopicOffsetRecord() {
    super(QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET);
  }

  /** Create a detached, initialised QuarantineTopicOffsetRecord */
  public QuarantineTopicOffsetRecord(
      String resourceType,
      Integer resourceId,
      Integer datasetId,
      Integer partitionNumber,
      String topic,
      Long offset,
      LocalDateTime lastModified) {
    super(QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET);

    setResourceType(resourceType);
    setResourceId(resourceId);
    setDatasetId(datasetId);
    setPartitionNumber(partitionNumber);
    setTopic(topic);
    setOffset(offset);
    setLastModified(lastModified);
  }
}
