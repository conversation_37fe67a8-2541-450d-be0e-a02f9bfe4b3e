/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables;

import com.nexla.db.monitoring.schema.DefaultSchema;
import com.nexla.db.monitoring.schema.Keys;
import com.nexla.db.monitoring.schema.tables.records.TopicOffsetRecord;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class TopicOffset extends TableImpl<TopicOffsetRecord> {

  private static final long serialVersionUID = 1L;

  /** The reference instance of <code>topic_offset</code> */
  public static final TopicOffset TOPIC_OFFSET = new TopicOffset();

  /** The class holding records for this type */
  @Override
  public Class<TopicOffsetRecord> getRecordType() {
    return TopicOffsetRecord.class;
  }

  /** The column <code>topic_offset.resource_type</code>. */
  public final TableField<TopicOffsetRecord, String> RESOURCE_TYPE =
      createField(DSL.name("resource_type"), SQLDataType.VARCHAR(10).nullable(false), this, "");

  /** The column <code>topic_offset.resource_id</code>. */
  public final TableField<TopicOffsetRecord, Integer> RESOURCE_ID =
      createField(DSL.name("resource_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>topic_offset.dataset_id</code>. */
  public final TableField<TopicOffsetRecord, Integer> DATASET_ID =
      createField(DSL.name("dataset_id"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>topic_offset.partition_number</code>. */
  public final TableField<TopicOffsetRecord, Integer> PARTITION_NUMBER =
      createField(DSL.name("partition_number"), SQLDataType.INTEGER.nullable(false), this, "");

  /** The column <code>topic_offset.topic</code>. */
  public final TableField<TopicOffsetRecord, String> TOPIC =
      createField(DSL.name("topic"), SQLDataType.VARCHAR(300).nullable(false), this, "");

  /** The column <code>topic_offset.offset</code>. */
  public final TableField<TopicOffsetRecord, Long> OFFSET =
      createField(
          DSL.name("offset"),
          SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", SQLDataType.BIGINT)),
          this,
          "");

  /** The column <code>topic_offset.last_modified</code>. */
  public final TableField<TopicOffsetRecord, LocalDateTime> LAST_MODIFIED =
      createField(
          DSL.name("last_modified"),
          SQLDataType.LOCALDATETIME(3)
              .nullable(false)
              .defaultValue(DSL.field("current_timestamp(3)", SQLDataType.LOCALDATETIME)),
          this,
          "");

  private TopicOffset(Name alias, Table<TopicOffsetRecord> aliased) {
    this(alias, aliased, null);
  }

  private TopicOffset(Name alias, Table<TopicOffsetRecord> aliased, Field<?>[] parameters) {
    super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
  }

  /** Create an aliased <code>topic_offset</code> table reference */
  public TopicOffset(String alias) {
    this(DSL.name(alias), TOPIC_OFFSET);
  }

  /** Create an aliased <code>topic_offset</code> table reference */
  public TopicOffset(Name alias) {
    this(alias, TOPIC_OFFSET);
  }

  /** Create a <code>topic_offset</code> table reference */
  public TopicOffset() {
    this(DSL.name("topic_offset"), null);
  }

  public <O extends Record> TopicOffset(Table<O> child, ForeignKey<O, TopicOffsetRecord> key) {
    super(child, key, TOPIC_OFFSET);
  }

  @Override
  public Schema getSchema() {
    return DefaultSchema.DEFAULT_SCHEMA;
  }

  @Override
  public UniqueKey<TopicOffsetRecord> getPrimaryKey() {
    return Keys.KEY_TOPIC_OFFSET_PRIMARY;
  }

  @Override
  public List<UniqueKey<TopicOffsetRecord>> getKeys() {
    return Arrays.<UniqueKey<TopicOffsetRecord>>asList(Keys.KEY_TOPIC_OFFSET_PRIMARY);
  }

  @Override
  public TopicOffset as(String alias) {
    return new TopicOffset(DSL.name(alias), this);
  }

  @Override
  public TopicOffset as(Name alias) {
    return new TopicOffset(alias, this);
  }

  /** Rename this table */
  @Override
  public TopicOffset rename(String name) {
    return new TopicOffset(DSL.name(name), null);
  }

  /** Rename this table */
  @Override
  public TopicOffset rename(Name name) {
    return new TopicOffset(name, null);
  }

  // -------------------------------------------------------------------------
  // Row7 type methods
  // -------------------------------------------------------------------------

  @Override
  public Row7<String, Integer, Integer, Integer, String, Long, LocalDateTime> fieldsRow() {
    return (Row7) super.fieldsRow();
  }
}
