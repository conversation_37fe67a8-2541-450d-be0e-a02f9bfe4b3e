/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema;

import com.nexla.db.monitoring.schema.tables.AccountMetricsDaily;
import com.nexla.db.monitoring.schema.tables.ConnectorState;
import com.nexla.db.monitoring.schema.tables.CustomFlowDaily;
import com.nexla.db.monitoring.schema.tables.CustomFlowHourly;
import com.nexla.db.monitoring.schema.tables.CustomFlowState;
import com.nexla.db.monitoring.schema.tables.DataMonitorDaily;
import com.nexla.db.monitoring.schema.tables.DataMonitorHourly;
import com.nexla.db.monitoring.schema.tables.DataMonitorNotifications;
import com.nexla.db.monitoring.schema.tables.DataSetsTxDaily;
import com.nexla.db.monitoring.schema.tables.DataSetsTxHourly;
import com.nexla.db.monitoring.schema.tables.DataSinksDaily;
import com.nexla.db.monitoring.schema.tables.DataSinksHourly;
import com.nexla.db.monitoring.schema.tables.DataSourcesDaily;
import com.nexla.db.monitoring.schema.tables.DataSourcesHourly;
import com.nexla.db.monitoring.schema.tables.ErrorStatusMetrics;
import com.nexla.db.monitoring.schema.tables.FileReingestionRequest;
import com.nexla.db.monitoring.schema.tables.FileSinkState;
import com.nexla.db.monitoring.schema.tables.FileSourceState;
import com.nexla.db.monitoring.schema.tables.FlowMetricsDaily;
import com.nexla.db.monitoring.schema.tables.QuarantineFiles;
import com.nexla.db.monitoring.schema.tables.QuarantineTopicAggregationResult;
import com.nexla.db.monitoring.schema.tables.QuarantineTopicOffset;
import com.nexla.db.monitoring.schema.tables.ScriptConfig;
import com.nexla.db.monitoring.schema.tables.TopicOffset;
import java.util.Arrays;
import java.util.List;
import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DefaultSchema extends SchemaImpl {

  private static final long serialVersionUID = *********;

  /** The reference instance of <code>DEFAULT_SCHEMA</code> */
  public static final DefaultSchema DEFAULT_SCHEMA = new DefaultSchema();

  /** The table <code>account_metrics_daily</code>. */
  public final AccountMetricsDaily ACCOUNT_METRICS_DAILY =
      AccountMetricsDaily.ACCOUNT_METRICS_DAILY;

  /** The table <code>connector_state</code>. */
  public final ConnectorState CONNECTOR_STATE = ConnectorState.CONNECTOR_STATE;

  /** The table <code>custom_flow_daily</code>. */
  public final CustomFlowDaily CUSTOM_FLOW_DAILY = CustomFlowDaily.CUSTOM_FLOW_DAILY;

  /** The table <code>custom_flow_hourly</code>. */
  public final CustomFlowHourly CUSTOM_FLOW_HOURLY = CustomFlowHourly.CUSTOM_FLOW_HOURLY;

  /** The table <code>custom_flow_state</code>. */
  public final CustomFlowState CUSTOM_FLOW_STATE = CustomFlowState.CUSTOM_FLOW_STATE;

  /** The table <code>data_monitor_daily</code>. */
  public final DataMonitorDaily DATA_MONITOR_DAILY = DataMonitorDaily.DATA_MONITOR_DAILY;

  /** The table <code>data_monitor_hourly</code>. */
  public final DataMonitorHourly DATA_MONITOR_HOURLY = DataMonitorHourly.DATA_MONITOR_HOURLY;

  /** The table <code>data_monitor_notifications</code>. */
  public final DataMonitorNotifications DATA_MONITOR_NOTIFICATIONS =
      DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS;

  /** The table <code>data_sets_tx_daily</code>. */
  public final DataSetsTxDaily DATA_SETS_TX_DAILY = DataSetsTxDaily.DATA_SETS_TX_DAILY;

  /** The table <code>data_sets_tx_hourly</code>. */
  public final DataSetsTxHourly DATA_SETS_TX_HOURLY = DataSetsTxHourly.DATA_SETS_TX_HOURLY;

  /** The table <code>data_sinks_daily</code>. */
  public final DataSinksDaily DATA_SINKS_DAILY = DataSinksDaily.DATA_SINKS_DAILY;

  /** The table <code>data_sinks_hourly</code>. */
  public final DataSinksHourly DATA_SINKS_HOURLY = DataSinksHourly.DATA_SINKS_HOURLY;

  /** The table <code>data_sources_daily</code>. */
  public final DataSourcesDaily DATA_SOURCES_DAILY = DataSourcesDaily.DATA_SOURCES_DAILY;

  /** The table <code>data_sources_hourly</code>. */
  public final DataSourcesHourly DATA_SOURCES_HOURLY = DataSourcesHourly.DATA_SOURCES_HOURLY;

  /** The table <code>error_status_metrics</code>. */
  public final ErrorStatusMetrics ERROR_STATUS_METRICS = ErrorStatusMetrics.ERROR_STATUS_METRICS;

  /** The table <code>file_reingestion_request</code>. */
  public final FileReingestionRequest FILE_REINGESTION_REQUEST =
      FileReingestionRequest.FILE_REINGESTION_REQUEST;

  /** The table <code>file_sink_state</code>. */
  public final FileSinkState FILE_SINK_STATE = FileSinkState.FILE_SINK_STATE;

  /** The table <code>file_source_state</code>. */
  public final FileSourceState FILE_SOURCE_STATE = FileSourceState.FILE_SOURCE_STATE;

  /** The table <code>flow_metrics_daily</code>. */
  public final FlowMetricsDaily FLOW_METRICS_DAILY = FlowMetricsDaily.FLOW_METRICS_DAILY;

  /** The table <code>quarantine_files</code>. */
  public final QuarantineFiles QUARANTINE_FILES = QuarantineFiles.QUARANTINE_FILES;

  /** The table <code>quarantine_topic_offset</code>. */
  public final QuarantineTopicOffset QUARANTINE_TOPIC_OFFSET =
      QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET;

  /** The table <code>script_config</code>. */
  public final ScriptConfig SCRIPT_CONFIG = ScriptConfig.SCRIPT_CONFIG;

  /** The table <code>topic_offset</code>. */
  public final TopicOffset TOPIC_OFFSET = TopicOffset.TOPIC_OFFSET;

  /** The table <code>quarantine_topic_aggregation_result</code>. */
  public final QuarantineTopicAggregationResult QUARANTINE_TOPIC_AGGREGATION_RESULT =
      QuarantineTopicAggregationResult.QUARANTINE_TOPIC_AGGREGATION_RESULT;

  /** No further instances allowed */
  private DefaultSchema() {
    super("", null);
  }

  @Override
  public Catalog getCatalog() {
    return DefaultCatalog.DEFAULT_CATALOG;
  }

  @Override
  public final List<Table<?>> getTables() {
    return Arrays.<Table<?>>asList(
        AccountMetricsDaily.ACCOUNT_METRICS_DAILY,
        ConnectorState.CONNECTOR_STATE,
        CustomFlowDaily.CUSTOM_FLOW_DAILY,
        CustomFlowHourly.CUSTOM_FLOW_HOURLY,
        CustomFlowState.CUSTOM_FLOW_STATE,
        DataMonitorDaily.DATA_MONITOR_DAILY,
        DataMonitorHourly.DATA_MONITOR_HOURLY,
        DataMonitorNotifications.DATA_MONITOR_NOTIFICATIONS,
        DataSetsTxDaily.DATA_SETS_TX_DAILY,
        DataSetsTxHourly.DATA_SETS_TX_HOURLY,
        DataSinksDaily.DATA_SINKS_DAILY,
        DataSinksHourly.DATA_SINKS_HOURLY,
        DataSourcesDaily.DATA_SOURCES_DAILY,
        DataSourcesHourly.DATA_SOURCES_HOURLY,
        ErrorStatusMetrics.ERROR_STATUS_METRICS,
        FileReingestionRequest.FILE_REINGESTION_REQUEST,
        FileSinkState.FILE_SINK_STATE,
        FileSourceState.FILE_SOURCE_STATE,
        FlowMetricsDaily.FLOW_METRICS_DAILY,
        QuarantineFiles.QUARANTINE_FILES,
        QuarantineTopicOffset.QUARANTINE_TOPIC_OFFSET,
        ScriptConfig.SCRIPT_CONFIG,
        TopicOffset.TOPIC_OFFSET,
        QuarantineTopicAggregationResult.QUARANTINE_TOPIC_AGGREGATION_RESULT);
  }
}
