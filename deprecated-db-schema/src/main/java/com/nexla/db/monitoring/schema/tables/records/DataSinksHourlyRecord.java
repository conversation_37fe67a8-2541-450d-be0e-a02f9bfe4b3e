/*
 * This file is generated by jOOQ.
 */
package com.nexla.db.monitoring.schema.tables.records;

import com.nexla.db.monitoring.schema.tables.DataSinksHourly;
import java.time.LocalDateTime;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.UpdatableRecordImpl;

/** This class is generated by jOOQ. */
@SuppressWarnings({"all", "unchecked", "rawtypes"})
public class DataSinksHourlyRecord extends UpdatableRecordImpl<DataSinksHourlyRecord>
    implements Record15<
        Long,
        Integer,
        LocalDateTime,
        Long,
        Long,
        LocalDateTime,
        LocalDateTime,
        Integer,
        Integer,
        Integer,
        Long,
        Long,
        LocalDateTime,
        Integer,
        Integer> {

  private static final long serialVersionUID = 1766388721;

  /** Setter for <code>data_sinks_hourly.id</code>. */
  public void setId(Long value) {
    set(0, value);
  }

  /** Getter for <code>data_sinks_hourly.id</code>. */
  public Long getId() {
    return (Long) get(0);
  }

  /** Setter for <code>data_sinks_hourly.resource_id</code>. */
  public void setResourceId(Integer value) {
    set(1, value);
  }

  /** Getter for <code>data_sinks_hourly.resource_id</code>. */
  public Integer getResourceId() {
    return (Integer) get(1);
  }

  /** Setter for <code>data_sinks_hourly.reporting_hour</code>. */
  public void setReportingHour(LocalDateTime value) {
    set(2, value);
  }

  /** Getter for <code>data_sinks_hourly.reporting_hour</code>. */
  public LocalDateTime getReportingHour() {
    return (LocalDateTime) get(2);
  }

  /** Setter for <code>data_sinks_hourly.row_count</code>. */
  public void setRowCount(Long value) {
    set(3, value);
  }

  /** Getter for <code>data_sinks_hourly.row_count</code>. */
  public Long getRowCount() {
    return (Long) get(3);
  }

  /** Setter for <code>data_sinks_hourly.data_volume_bytes</code>. */
  public void setDataVolumeBytes(Long value) {
    set(4, value);
  }

  /** Getter for <code>data_sinks_hourly.data_volume_bytes</code>. */
  public Long getDataVolumeBytes() {
    return (Long) get(4);
  }

  /** Setter for <code>data_sinks_hourly.updated_at</code>. */
  public void setUpdatedAt(LocalDateTime value) {
    set(5, value);
  }

  /** Getter for <code>data_sinks_hourly.updated_at</code>. */
  public LocalDateTime getUpdatedAt() {
    return (LocalDateTime) get(5);
  }

  /** Setter for <code>data_sinks_hourly.created_at</code>. */
  public void setCreatedAt(LocalDateTime value) {
    set(6, value);
  }

  /** Getter for <code>data_sinks_hourly.created_at</code>. */
  public LocalDateTime getCreatedAt() {
    return (LocalDateTime) get(6);
  }

  /** Setter for <code>data_sinks_hourly.org_id</code>. */
  public void setOrgId(Integer value) {
    set(7, value);
  }

  /** Getter for <code>data_sinks_hourly.org_id</code>. */
  public Integer getOrgId() {
    return (Integer) get(7);
  }

  /** Setter for <code>data_sinks_hourly.owner_id</code>. */
  public void setOwnerId(Integer value) {
    set(8, value);
  }

  /** Getter for <code>data_sinks_hourly.owner_id</code>. */
  public Integer getOwnerId() {
    return (Integer) get(8);
  }

  /** Setter for <code>data_sinks_hourly.data_set_id</code>. */
  public void setDataSetId(Integer value) {
    set(9, value);
  }

  /** Getter for <code>data_sinks_hourly.data_set_id</code>. */
  public Integer getDataSetId() {
    return (Integer) get(9);
  }

  /** Setter for <code>data_sinks_hourly.error_count</code>. */
  public void setErrorCount(Long value) {
    set(10, value);
  }

  /** Getter for <code>data_sinks_hourly.error_count</code>. */
  public Long getErrorCount() {
    return (Long) get(10);
  }

  /** Setter for <code>data_sinks_hourly.run_id</code>. */
  public void setRunId(Long value) {
    set(11, value);
  }

  /** Getter for <code>data_sinks_hourly.run_id</code>. */
  public Long getRunId() {
    return (Long) get(11);
  }

  /** Setter for <code>data_sinks_hourly.max_last_written</code>. */
  public void setMaxLastWritten(LocalDateTime value) {
    set(12, value);
  }

  /** Getter for <code>data_sinks_hourly.max_last_written</code>. */
  public LocalDateTime getMaxLastWritten() {
    return (LocalDateTime) get(12);
  }

  /** Setter for <code>data_sinks_hourly.origin_node_id</code>. */
  public void setOriginNodeId(Integer value) {
    set(13, value);
  }

  /** Getter for <code>data_sinks_hourly.origin_node_id</code>. */
  public Integer getOriginNodeId() {
    return (Integer) get(13);
  }

  /** Setter for <code>data_sinks_hourly.flow_node_id</code>. */
  public void setFlowNodeId(Integer value) {
    set(14, value);
  }

  /** Getter for <code>data_sinks_hourly.flow_node_id</code>. */
  public Integer getFlowNodeId() {
    return (Integer) get(14);
  }

  // -------------------------------------------------------------------------
  // Primary key information
  // -------------------------------------------------------------------------

  @Override
  public Record1<Long> key() {
    return (Record1) super.key();
  }

  // -------------------------------------------------------------------------
  // Record15 type implementation
  // -------------------------------------------------------------------------

  @Override
  public Row15<
          Long,
          Integer,
          LocalDateTime,
          Long,
          Long,
          LocalDateTime,
          LocalDateTime,
          Integer,
          Integer,
          Integer,
          Long,
          Long,
          LocalDateTime,
          Integer,
          Integer>
      fieldsRow() {
    return (Row15) super.fieldsRow();
  }

  @Override
  public Row15<
          Long,
          Integer,
          LocalDateTime,
          Long,
          Long,
          LocalDateTime,
          LocalDateTime,
          Integer,
          Integer,
          Integer,
          Long,
          Long,
          LocalDateTime,
          Integer,
          Integer>
      valuesRow() {
    return (Row15) super.valuesRow();
  }

  @Override
  public Field<Long> field1() {
    return DataSinksHourly.DATA_SINKS_HOURLY.ID;
  }

  @Override
  public Field<Integer> field2() {
    return DataSinksHourly.DATA_SINKS_HOURLY.RESOURCE_ID;
  }

  @Override
  public Field<LocalDateTime> field3() {
    return DataSinksHourly.DATA_SINKS_HOURLY.REPORTING_HOUR;
  }

  @Override
  public Field<Long> field4() {
    return DataSinksHourly.DATA_SINKS_HOURLY.ROW_COUNT;
  }

  @Override
  public Field<Long> field5() {
    return DataSinksHourly.DATA_SINKS_HOURLY.DATA_VOLUME_BYTES;
  }

  @Override
  public Field<LocalDateTime> field6() {
    return DataSinksHourly.DATA_SINKS_HOURLY.UPDATED_AT;
  }

  @Override
  public Field<LocalDateTime> field7() {
    return DataSinksHourly.DATA_SINKS_HOURLY.CREATED_AT;
  }

  @Override
  public Field<Integer> field8() {
    return DataSinksHourly.DATA_SINKS_HOURLY.ORG_ID;
  }

  @Override
  public Field<Integer> field9() {
    return DataSinksHourly.DATA_SINKS_HOURLY.OWNER_ID;
  }

  @Override
  public Field<Integer> field10() {
    return DataSinksHourly.DATA_SINKS_HOURLY.DATA_SET_ID;
  }

  @Override
  public Field<Long> field11() {
    return DataSinksHourly.DATA_SINKS_HOURLY.ERROR_COUNT;
  }

  @Override
  public Field<Long> field12() {
    return DataSinksHourly.DATA_SINKS_HOURLY.RUN_ID;
  }

  @Override
  public Field<LocalDateTime> field13() {
    return DataSinksHourly.DATA_SINKS_HOURLY.MAX_LAST_WRITTEN;
  }

  @Override
  public Field<Integer> field14() {
    return DataSinksHourly.DATA_SINKS_HOURLY.ORIGIN_NODE_ID;
  }

  @Override
  public Field<Integer> field15() {
    return DataSinksHourly.DATA_SINKS_HOURLY.FLOW_NODE_ID;
  }

  @Override
  public Long component1() {
    return getId();
  }

  @Override
  public Integer component2() {
    return getResourceId();
  }

  @Override
  public LocalDateTime component3() {
    return getReportingHour();
  }

  @Override
  public Long component4() {
    return getRowCount();
  }

  @Override
  public Long component5() {
    return getDataVolumeBytes();
  }

  @Override
  public LocalDateTime component6() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime component7() {
    return getCreatedAt();
  }

  @Override
  public Integer component8() {
    return getOrgId();
  }

  @Override
  public Integer component9() {
    return getOwnerId();
  }

  @Override
  public Integer component10() {
    return getDataSetId();
  }

  @Override
  public Long component11() {
    return getErrorCount();
  }

  @Override
  public Long component12() {
    return getRunId();
  }

  @Override
  public LocalDateTime component13() {
    return getMaxLastWritten();
  }

  @Override
  public Integer component14() {
    return getOriginNodeId();
  }

  @Override
  public Integer component15() {
    return getFlowNodeId();
  }

  @Override
  public Long value1() {
    return getId();
  }

  @Override
  public Integer value2() {
    return getResourceId();
  }

  @Override
  public LocalDateTime value3() {
    return getReportingHour();
  }

  @Override
  public Long value4() {
    return getRowCount();
  }

  @Override
  public Long value5() {
    return getDataVolumeBytes();
  }

  @Override
  public LocalDateTime value6() {
    return getUpdatedAt();
  }

  @Override
  public LocalDateTime value7() {
    return getCreatedAt();
  }

  @Override
  public Integer value8() {
    return getOrgId();
  }

  @Override
  public Integer value9() {
    return getOwnerId();
  }

  @Override
  public Integer value10() {
    return getDataSetId();
  }

  @Override
  public Long value11() {
    return getErrorCount();
  }

  @Override
  public Long value12() {
    return getRunId();
  }

  @Override
  public LocalDateTime value13() {
    return getMaxLastWritten();
  }

  @Override
  public Integer value14() {
    return getOriginNodeId();
  }

  @Override
  public Integer value15() {
    return getFlowNodeId();
  }

  @Override
  public DataSinksHourlyRecord value1(Long value) {
    setId(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value2(Integer value) {
    setResourceId(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value3(LocalDateTime value) {
    setReportingHour(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value4(Long value) {
    setRowCount(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value5(Long value) {
    setDataVolumeBytes(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value6(LocalDateTime value) {
    setUpdatedAt(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value7(LocalDateTime value) {
    setCreatedAt(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value8(Integer value) {
    setOrgId(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value9(Integer value) {
    setOwnerId(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value10(Integer value) {
    setDataSetId(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value11(Long value) {
    setErrorCount(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value12(Long value) {
    setRunId(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value13(LocalDateTime value) {
    setMaxLastWritten(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value14(Integer value) {
    setOriginNodeId(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord value15(Integer value) {
    setFlowNodeId(value);
    return this;
  }

  @Override
  public DataSinksHourlyRecord values(
      Long value1,
      Integer value2,
      LocalDateTime value3,
      Long value4,
      Long value5,
      LocalDateTime value6,
      LocalDateTime value7,
      Integer value8,
      Integer value9,
      Integer value10,
      Long value11,
      Long value12,
      LocalDateTime value13,
      Integer value14,
      Integer value15) {
    value1(value1);
    value2(value2);
    value3(value3);
    value4(value4);
    value5(value5);
    value6(value6);
    value7(value7);
    value8(value8);
    value9(value9);
    value10(value10);
    value11(value11);
    value12(value12);
    value13(value13);
    value14(value14);
    value15(value15);
    return this;
  }

  // -------------------------------------------------------------------------
  // Constructors
  // -------------------------------------------------------------------------

  /** Create a detached DataSinksHourlyRecord */
  public DataSinksHourlyRecord() {
    super(DataSinksHourly.DATA_SINKS_HOURLY);
  }

  /** Create a detached, initialised DataSinksHourlyRecord */
  public DataSinksHourlyRecord(
      Long id,
      Integer resourceId,
      LocalDateTime reportingHour,
      Long rowCount,
      Long dataVolumeBytes,
      LocalDateTime updatedAt,
      LocalDateTime createdAt,
      Integer orgId,
      Integer ownerId,
      Integer dataSetId,
      Long errorCount,
      Long runId,
      LocalDateTime maxLastWritten,
      Integer originNodeId,
      Integer flowNodeId) {
    super(DataSinksHourly.DATA_SINKS_HOURLY);

    setId(id);
    setResourceId(resourceId);
    setReportingHour(reportingHour);
    setRowCount(rowCount);
    setDataVolumeBytes(dataVolumeBytes);
    setUpdatedAt(updatedAt);
    setCreatedAt(createdAt);
    setOrgId(orgId);
    setOwnerId(ownerId);
    setDataSetId(dataSetId);
    setErrorCount(errorCount);
    setRunId(runId);
    setMaxLastWritten(maxLastWritten);
    setOriginNodeId(originNodeId);
    setFlowNodeId(flowNodeId);
  }
}
