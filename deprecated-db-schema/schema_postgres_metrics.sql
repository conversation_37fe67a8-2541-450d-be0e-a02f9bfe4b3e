CREATE TABLE custom_flow_state (
    id bigserial PRIMARY KEY,
    resource_id INT,
    name <PERSON><PERSON><PERSON><PERSON>(500),
    size_value BIGINT,
    record_count BIGINT,
    error_count BIGINT,
    run_id BIGINT,
    org_id int DEFAULT NULL,
    owner_id int DEFAULT NULL,
    aggregated BOOLEAN DEFAULT false,
    updated_at TIMESTAMP(3) NULL,
    created_at TIMESTAMP(3) NULL
);

CREATE TABLE custom_flow_daily (
    id bigserial PRIMARY KEY,
    resource_id INT NOT NULL,
    run_id BIGINT NULL,
    row_count bigint DEFAULT NULL,
    error_count bigint DEFAULT NULL,
    pipeline_count bigint DEFAULT NULL,
    size_value bigint DEFAULT NULL,
    org_id int DEFAULT NULL,
    owner_id int DEFAULT NULL,
    reporting_date timestamp(3) NOT NULL,
    updated_at TIMESTAMP(3) NULL,
    created_at TIMESTAMP(3) NULL
);


CREATE INDEX custom_flow_state_daily_date ON custom_flow_daily (reporting_date asc)

CREATE INDEX custom_flow_state_daily_org_id ON custom_flow_daily (org_id asc, reporting_date asc)

CREATE INDEX custom_flow_state_daily_owner_id ON custom_flow_daily (owner_id asc, reporting_date asc)


CREATE TABLE custom_flow_hourly (
    id bigserial PRIMARY KEY,
    resource_id INT NOT NULL,
    run_id BIGINT NULL,
    row_count bigint DEFAULT NULL,
    error_count bigint DEFAULT NULL,
    pipeline_count bigint DEFAULT NULL,
    size_value bigint DEFAULT NULL,
    org_id int DEFAULT NULL,
    owner_id int DEFAULT NULL,
    reporting_hour TIMESTAMP(3) NOT NULL,
    updated_at TIMESTAMP(3) NULL,
    created_at TIMESTAMP(3) NULL
);

create INDEX custom_flow_state_hourly_date ON custom_flow_hourly (reporting_hour ASC)

create INDEX custom_flow_state_hourly_org_id ON custom_flow_hourly (org_id ASC, reporting_hour ASC)

create INDEX custom_flow_state_hourly_owner_id ON custom_flow_hourly (owner_id ASC, reporting_hour ASC)

create UNIQUE INDEX custom_flow_data_sources_hourly_id ON custom_flow_hourly (resource_id ASC, reporting_hour ASC, run_id)

CREATE TABLE quarantine_topic_aggregation_result (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    resource_id int(11) NOT NULL,
    origin_node_id int(11) NOT NULL,
    flow_node_id int(11) NOT NULL,
    resource_type varchar(10) NOT NULL,
    topic varchar(300) NOT NULL,
    run_id BIGINT NOT NULL,
    aggregation_result text NOT NULL,
    created_at timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    last_modified timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    PRIMARY KEY (`id`)
);