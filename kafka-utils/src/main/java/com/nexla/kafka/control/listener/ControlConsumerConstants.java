package com.nexla.kafka.control.listener;

public class ControlConsumerConstants {

  public static final String SOURCE_TYPE_S3 = "s3";

  public static final String TOPIC = "topic";
  public static final String TOPICS = "topics";

  public static final String SINK_CONFIG_BUCKET = "bucket";

  public static final String SOURCE_ID = "source_id";

  public static final String SOURCE_CONTROL = "source";
  public static final String DATASET_CONTROL = "dataset";
  public static final String SINK_CONTROL = "sink";

  public static final String CONNECTOR_CONTROL = "connector";
}
