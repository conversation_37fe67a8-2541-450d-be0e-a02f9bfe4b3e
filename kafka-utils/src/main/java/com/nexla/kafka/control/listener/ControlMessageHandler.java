package com.nexla.kafka.control.listener;

import static com.nexla.telemetry.utils.ExecutionTelemetryUtils.metricSet;

import com.nexla.control.message.ControlMessage;
import com.nexla.telemetry.utils.ExecutionMetricSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Generic interface for handling control messages. Any new handler should define a new message type
 * in com.nexla.control.message package
 */
public abstract class ControlMessageHandler<T extends ControlMessage> {

  private static final Logger logger = LoggerFactory.getLogger(ControlMessageHandler.class);

  public void activate(T message) {}

  public void create(T message) {}

  public void runNow(T message) {}

  public void pause(T message) {}

  public void update(T message) {}

  public void serviceOnCompletion(T message) {}

  public void delete(T message) {
    pause(message);
  }

  private final ExecutionMetricSet handleMessageMetrics =
      metricSet(this.getClass(), "handleMessage");
  private final ExecutionMetricSet createMetrics = metricSet(this.getClass(), "create");
  private final ExecutionMetricSet activateMetrics = metricSet(this.getClass(), "activate");
  private final ExecutionMetricSet runNowMetrics = metricSet(this.getClass(), "runNow");
  private final ExecutionMetricSet updateMetrics = metricSet(this.getClass(), "update");
  private final ExecutionMetricSet pauseMetrics = metricSet(this.getClass(), "pause");
  private final ExecutionMetricSet deleteMetrics = metricSet(this.getClass(), "delete");
  private final ExecutionMetricSet serviceOnCompletionMetrics =
      metricSet(this.getClass(), "serviceOnCompletion");

  public void handleMessage(T m) {
    handleMessageMetrics.track(
        () -> {
          switch (m.getEventType()) {
            case ACTIVATE:
              {
                activateMetrics.track(() -> activate(m));
                break;
              }
            case RUN_NOW:
              {
                runNowMetrics.track(() -> runNow(m));
                break;
              }
            case PAUSE:
              {
                pauseMetrics.track(() -> pause(m));
                break;
              }
            case CREATE:
              {
                createMetrics.track(() -> create(m));
                break;
              }
            case UPDATE:
              {
                updateMetrics.track(() -> update(m));
                break;
              }
            case SERVICE_ON_COMPLETION:
              {
                serviceOnCompletionMetrics.track(() -> serviceOnCompletion(m));
                break;
              }
            case DELETE:
              {
                deleteMetrics.track(() -> delete(m));
                break;
              }
            default:
              // do nothing
          }
        });
  }
}
