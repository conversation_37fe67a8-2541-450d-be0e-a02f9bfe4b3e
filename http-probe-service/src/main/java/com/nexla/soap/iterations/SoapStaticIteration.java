package com.nexla.soap.iterations;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.exception.ProbeRetriableException;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.HttpCallParameters;
import com.nexla.connector.config.rest.RestHeaders;
import com.nexla.connector.config.soap.SoapIterationConfig;
import com.nexla.probe.http.HttpSenderResponse;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.pojo.UrlBody;
import com.nexla.soap.SoapIteration;
import com.nexla.soap.SoapIterationOffset;
import com.nexla.soap.SoapWsdlParser;
import com.nexla.soap.pojo.SoapParsedData;
import com.nexla.soap.pojo.SoapResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import org.joda.time.DateTime;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;

public class SoapStaticIteration extends SoapIteration<SoapIterationOffset> {

	private final SoapWsdlParser parser;

	public SoapStaticIteration(
		SoapIterationConfig config,
		NexlaPool<RequestSender> senderPool,
		SoapWsdlParser parser,
		NexlaLogger logger
	) {
		super(config, senderPool, logger);
		this.parser = parser;
	}

	@Override
	@SneakyThrows
	public UrlBody getRequestUrlBody(Map<String, String> substituteMap) {
		Map<String, String> params = Maps.newHashMap(config.soapFormParams);
		params.putAll(substituteMap);
		Map<String, String> xpath = EntryStream.of(params)
			.mapKeys(k -> "xpath:/" + k)
			.toMap();
		return parser.getUrlBody(
			config.soapConf.getWsdlUrl(),
			config.soapConf,
			xpath
		);
	}

	public HttpSenderResponse readResponse(RequestSender sender, UrlBody urlBody) {
		try {
			RestHeaders headers = new RestHeaders(Collections.emptyMap(), ofNullable(MediaType.TEXT_XML_VALUE), empty());
			// XXX: does URL encoding as expected.
			HttpCallParameters cp = new HttpCallParameters(urlBody.getUrl(), false, HttpMethod.POST, ofNullable(urlBody.getBody()), empty(), headers);
			return sender.send(cp);
		} catch (Exception exp) {
			logException(exp, urlBody.getUrl(), ofNullable(urlBody.getBody()));
			throw new ProbeRetriableException(urlBody.getUrl(), exp);
		}
	}

	@Override
	public Class<SoapIterationOffset> getOffsetClass() {
		return SoapIterationOffset.class;
	}

	@Override
	public SoapParsedData createCallResult(
		EntryStream<Integer, NexlaMessage> recordStream,
		SoapIterationOffset offset
	) {
		List<SoapResultEntry> records = EntryStream.of(recordStream)
			.mapKeyValue((recordOffset, data) -> new SoapResultEntry(data.getRawMessage(), recordOffset, null, offset))
			.toList();

		return new SoapParsedData(records, offset, empty());
	}

	@Override
	public SoapIterationOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return new SoapIterationOffset(0, empty(), parentMessageNumber, dateTime);
	}

}
