package com.nexla.rest.iterations;

import com.google.common.collect.Maps;
import com.nexla.common.NexlaMessage;
import com.nexla.common.logging.NexlaLogger;
import com.nexla.common.pool.NexlaPool;
import com.nexla.connector.config.rest.RestIterationConfig;
import com.nexla.probe.http.RequestSender;
import com.nexla.rest.RestIteration;
import com.nexla.rest.iterations.offsets.ResponseIdStringOffset;
import com.nexla.rest.pojo.ParsedData;
import com.nexla.rest.pojo.ResultEntry;
import lombok.SneakyThrows;
import one.util.streamex.EntryStream;
import one.util.streamex.StreamEx;
import org.joda.time.DateTime;
import org.springframework.http.HttpHeaders;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static com.nexla.probe.http.RestConnectorService.MAX_ERRORS_TO_LOG;

public class ResponseIdString extends RestIteration<ResponseIdStringOffset> {

	public static final String FAKE_STRING_ID = "-1";

	public ResponseIdString(RestIterationConfig config, NexlaPool<RequestSender> senderPool, NexlaLogger logger) {
		super(config, senderPool, logger);
	}

	@Override
	public Class<ResponseIdStringOffset> getOffsetClass() {
		return ResponseIdStringOffset.class;
	}

	@Override
	public Map<String, String> macroSubstitutionMap(ResponseIdStringOffset offset) {
		Map<String, String> substitutionMacroMap = new HashMap<>();
		if (config.paramIdMacro.isEmpty()) {
			return substitutionMacroMap;
		}
		substitutionMacroMap.put(config.paramIdMacro.get(),
				offset.getStringId().orElse(config.startStringIdFrom.orElseThrow()));
		return substitutionMacroMap;
	}

	@Override
	@SneakyThrows
	public Map<String, String> getParameters(ResponseIdStringOffset prevCtx) {
		Map<String, String> params = Maps.newHashMap();

		if (config.paramIdMacro.isPresent()) {
			return params;
		}

		prevCtx.getStringId().ifPresent(id -> params.put(config.paramId, id));
		return params;
	}

	@Override
	public ParsedData createCallResult(
		Optional<byte[]> responseBytes,
		HttpHeaders headers,
		EntryStream<Integer, NexlaMessage> recordStream,
		ResponseIdStringOffset offset,
		String url
	) {
		AtomicReference<String> lastStringId = new AtomicReference<>();
		List<ResultEntry> records = recordStream
			.mapKeyValue((offsetOnPage, data) -> {
				//todo we should set it as offset, but this is string id
				Object recordId = data.getRawMessage().get(config.responseId.get());
				if (recordId != null) {
					lastStringId.set(recordId.toString());
				}
				return new ResultEntry(data.getRawMessage(), offsetOnPage, offset.getStringId().orElse(FAKE_STRING_ID), offset, headers.toSingleValueMap(), null);
			})
			.toList();

		Optional<ResponseIdStringOffset> nextCallContext = Optional.ofNullable(lastStringId.get())
			.map(id -> getOffsetData(offset.getParentMessageNumber(), Optional.of(id), offset.getDateTime()))
			.filter(o -> !offset.getStringId().orElse(FAKE_STRING_ID).equals(config.endStringIdTo.orElse(null)));

		return new ParsedData(records, offset, nextCallContext);
	}

	@Override
	public ResponseIdStringOffset createStartOffset(Integer parentMessageNumber, DateTime dateTime) {
		return getOffsetData(parentMessageNumber, config.startStringIdFrom, dateTime);
	}

	private ResponseIdStringOffset getOffsetData(Integer parentMessageNumber, Optional<String> lastStringId, DateTime dateTime) {
		return new ResponseIdStringOffset(lastStringId, parentMessageNumber, dateTime);
	}

	@Override
	public StreamEx<NexlaMessage> filterErrors(StreamEx<NexlaMessage> recordStream) {
		AtomicInteger skippedCount = new AtomicInteger(0);
		return recordStream
			.filter(record -> {
				Object id = record.getRawMessage().get(config.responseId.get());
				if (id == null) {
					if ((skippedCount.get() < MAX_ERRORS_TO_LOG)) {
						getLogger().warn("Skipping record without id: id={} record={}", id, record);
						skippedCount.incrementAndGet();
					}
					return false;
				} else {
					return true;
				}
			});
	}

}
